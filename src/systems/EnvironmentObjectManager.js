/**
 * Manages the environment object system, including floor and wall object generation and placement.
 *
 * CURRENT IMPLEMENTATION NOTE:
 * This system is currently configured for the catacombs area, with support for:
 * - Floor Objects: stone_vase, stone_pillar, stone_rubble
 * - Wall Objects: vine, torch
 *
 * Other object types and biomes are defined but not actively used until they are fully implemented.
 */

import * as THREE from 'three';
import { mulberry32 } from '../generators/prefabs/shared.js';
import { getPrefabFunction } from '../prefabs/prefabs.js';

// --- Object Role Definitions ---
const OBJECT_ROLES = {
    DECORATION: 'decoration',     // Visual elements with no gameplay impact
    LIGHT: 'light',               // Objects that provide light
    DESTRUCTIBLE: 'destructible', // Objects that can be destroyed
    INTERACTIVE: 'interactive',   // Objects that can be interacted with
    HAZARD: 'hazard',             // Objects that can harm the player
    SPECIAL: 'special'            // Special objects with unique behaviors
};

// --- Object Placement Types ---
const PLACEMENT_TYPES = {
    WALL: 'wall',       // Placed on walls
    FLOOR: 'floor',     // Placed on floors
    CEILING: 'ceiling', // Placed on ceilings
    CORNER: 'corner',   // Placed in corners
    CENTER: 'center'    // Placed in the center of the room
};

// --- Object Role Assignments ---
const OBJECT_ROLE_MAP = {
    // Wall objects
    vine: OBJECT_ROLES.DECORATION,
    torch: OBJECT_ROLES.LIGHT,

    // Floor objects
    stone_vase: OBJECT_ROLES.DESTRUCTIBLE,
    stone_pillar: OBJECT_ROLES.DESTRUCTIBLE,
    stone_rubble: OBJECT_ROLES.DESTRUCTIBLE,
    ritual_circle: OBJECT_ROLES.SPECIAL,
    aether_torch: OBJECT_ROLES.LIGHT
};

// --- Object Group Templates ---
// These define balanced object group compositions for different room types
const OBJECT_GROUP_TEMPLATES = {
    // Normal rooms
    normal: [
        // Light-focused room
        {
            roles: [
                { type: OBJECT_ROLES.LIGHT, placement: PLACEMENT_TYPES.WALL, count: 6, placementDetail: 'wall' },
                { type: OBJECT_ROLES.DECORATION, placement: PLACEMENT_TYPES.WALL, count: 3, placementDetail: 'wall' },
                { type: OBJECT_ROLES.DESTRUCTIBLE, placement: PLACEMENT_TYPES.FLOOR, count: 3 }
            ],
            probability: 0.3
        },
        // Decoration-focused room
        {
            roles: [
                { type: OBJECT_ROLES.LIGHT, placement: PLACEMENT_TYPES.WALL, count: 3, placementDetail: 'wall' },
                { type: OBJECT_ROLES.DECORATION, placement: PLACEMENT_TYPES.WALL, count: 6, placementDetail: 'wall' },
                { type: OBJECT_ROLES.DESTRUCTIBLE, placement: PLACEMENT_TYPES.FLOOR, count: 4 }
            ],
            probability: 0.3
        },
        // Destructible-focused room
        {
            roles: [
                { type: OBJECT_ROLES.LIGHT, placement: PLACEMENT_TYPES.WALL, count: 4, placementDetail: 'wall' },
                { type: OBJECT_ROLES.DECORATION, placement: PLACEMENT_TYPES.WALL, count: 3, placementDetail: 'wall' },
                { type: OBJECT_ROLES.DESTRUCTIBLE, placement: PLACEMENT_TYPES.FLOOR, count: 6 }
            ],
            probability: 0.3
        },
        // Minimal room
        {
            roles: [
                { type: OBJECT_ROLES.LIGHT, placement: PLACEMENT_TYPES.WALL, count: 3, placementDetail: 'wall' },
                { type: OBJECT_ROLES.DESTRUCTIBLE, placement: PLACEMENT_TYPES.FLOOR, count: 2 }
            ],
            probability: 0.1
        }
    ],

    // Elite rooms
    elite: [
        // Symmetrical elite room
        {
            roles: [
                { type: OBJECT_ROLES.LIGHT, placement: PLACEMENT_TYPES.WALL, count: 6, placementDetail: 'wall' },
                { type: OBJECT_ROLES.DECORATION, placement: PLACEMENT_TYPES.WALL, count: 6, placementDetail: 'wall' },
                { type: OBJECT_ROLES.DESTRUCTIBLE, placement: PLACEMENT_TYPES.FLOOR, count: 4, placementDetail: 'corners' }
            ],
            probability: 0.5
        },
        // Hazardous elite room
        {
            roles: [
                { type: OBJECT_ROLES.LIGHT, placement: PLACEMENT_TYPES.WALL, count: 9, placementDetail: 'wall' },
                { type: OBJECT_ROLES.DECORATION, placement: PLACEMENT_TYPES.WALL, count: 6, placementDetail: 'wall' },
                { type: OBJECT_ROLES.DESTRUCTIBLE, placement: PLACEMENT_TYPES.FLOOR, count: 8 }
            ],
            probability: 0.5
        }
    ],

    // Boss rooms
    boss: [
        // Grand boss room
        {
            roles: [
                { type: OBJECT_ROLES.LIGHT, placement: PLACEMENT_TYPES.WALL, count: 12, placementDetail: 'wall' },
                { type: OBJECT_ROLES.DECORATION, placement: PLACEMENT_TYPES.WALL, count: 9, placementDetail: 'wall' },
                { type: OBJECT_ROLES.DESTRUCTIBLE, placement: PLACEMENT_TYPES.FLOOR, count: 4, placementDetail: 'corners' }
            ],
            probability: 1.0
        }
    ],

    // Start room
    start: [
        // Ritual circle room
        {
            roles: [
                { type: OBJECT_ROLES.LIGHT, placement: PLACEMENT_TYPES.WALL, count: 4 },
                { type: OBJECT_ROLES.SPECIAL, placement: PLACEMENT_TYPES.FLOOR, count: 1, placementDetail: 'center' }
            ],
            probability: 1.0
        }
    ]
};

// --- Biome-Specific Object Pools ---
// These define which objects can be placed in each biome
const BIOME_OBJECT_POOLS = {
    catacombs: {
        [OBJECT_ROLES.DECORATION]: {
            [PLACEMENT_TYPES.WALL]: ['vine'],
            [PLACEMENT_TYPES.FLOOR]: []
        },
        [OBJECT_ROLES.LIGHT]: {
            [PLACEMENT_TYPES.WALL]: ['torch'],
            [PLACEMENT_TYPES.FLOOR]: ['aether_torch']
        },
        [OBJECT_ROLES.DESTRUCTIBLE]: {
            [PLACEMENT_TYPES.WALL]: [],
            [PLACEMENT_TYPES.FLOOR]: ['stone_vase', 'stone_pillar', 'stone_rubble']
        },
        [OBJECT_ROLES.INTERACTIVE]: {
            [PLACEMENT_TYPES.WALL]: [],
            [PLACEMENT_TYPES.FLOOR]: []
        },
        [OBJECT_ROLES.HAZARD]: {
            [PLACEMENT_TYPES.WALL]: [],
            [PLACEMENT_TYPES.FLOOR]: []
        },
        [OBJECT_ROLES.SPECIAL]: {
            [PLACEMENT_TYPES.WALL]: [],
            [PLACEMENT_TYPES.FLOOR]: ['ritual_circle']
        }
    },

    // Other biomes would be defined here
    fungal_caverns: {
        [OBJECT_ROLES.DECORATION]: {
            [PLACEMENT_TYPES.WALL]: ['fungal_growth'],
            [PLACEMENT_TYPES.FLOOR]: ['small_mushroom']
        },
        [OBJECT_ROLES.LIGHT]: {
            [PLACEMENT_TYPES.WALL]: ['glowing_fungus'],
            [PLACEMENT_TYPES.FLOOR]: ['luminous_mushroom']
        },
        [OBJECT_ROLES.DESTRUCTIBLE]: {
            [PLACEMENT_TYPES.WALL]: [],
            [PLACEMENT_TYPES.FLOOR]: ['giant_mushroom', 'fungal_pod']
        },
        [OBJECT_ROLES.INTERACTIVE]: {
            [PLACEMENT_TYPES.WALL]: [],
            [PLACEMENT_TYPES.FLOOR]: ['spore_vent']
        },
        [OBJECT_ROLES.HAZARD]: {
            [PLACEMENT_TYPES.WALL]: [],
            [PLACEMENT_TYPES.FLOOR]: ['toxic_puddle']
        },
        [OBJECT_ROLES.SPECIAL]: {
            [PLACEMENT_TYPES.WALL]: [],
            [PLACEMENT_TYPES.FLOOR]: []
        }
    }
};

/**
 * Check if a position is within the valid area of a room shape
 * @param {number} x - X coordinate
 * @param {number} z - Z coordinate
 * @param {string} roomShape - Room shape key
 * @returns {boolean} True if position is valid for the room shape
 */
function _isPositionInRoomShape(x, z, roomShape) {
    if (!roomShape) {
        return true; // If no shape specified, allow all positions
    }

    // Room constants (matching DungeonHandler.js)
    const R = 14; // ROOM_WORLD_SIZE
    const H = 7;  // Half of ROOM_WORLD_SIZE

    switch (roomShape) {
        case 'SQUARE_1X1':
        case 'RECTANGULAR':
        case 'RECT_2X1':
        case 'RECT_1X2':
        case 'SQUARE_2X2':
        case 'RECT_3X1':
        case 'RECT_1X3':
        case 'RECT_3X2':
            // Simple rectangular shapes - use bounding box validation
            return true; // These are already handled by bounding box

        case 'L_SHAPE':
            // CORRECTED L-shape: vertical part (top-left) and horizontal part (bottom)
            // Based on floor segments: (-H,0,-H) with R×R and (0,0,H) with 2R×R
            // Valid areas:
            // - Vertical part (top-left): x from -R to 0, z from -R to 0
            // - Horizontal part (bottom): x from -R to R, z from 0 to R
            const inVerticalPart = (x >= -R && x <= 0 && z >= -R && z <= 0);
            const inHorizontalPart = (x >= -R && x <= R && z >= 0 && z <= R);
            return inVerticalPart || inHorizontalPart;

        case 'T_SHAPE':
            // T-shape: horizontal bar at top (3x1) and vertical stem at bottom (1x1)
            // Top bar: (-R-H to R+H, -R to 0)
            // Bottom stem: (-H to H, 0 to R)
            const inTopBar = (x >= -(R + H) && x <= (R + H) && z >= -R && z <= 0);
            const inBottomStem = (x >= -H && x <= H && z >= 0 && z <= R);
            return inTopBar || inBottomStem;

        case 'U_SHAPE_DOWN':
            // U-shape with opening at bottom: top bar (3x1) and two side legs (1x1 each)
            // Top bar: (-R-H to R+H, -R to 0)
            // Left leg: (-R-H to -H, 0 to R)
            // Right leg: (H to R+H, 0 to R)
            const inUTopBar = (x >= -(R + H) && x <= (R + H) && z >= -R && z <= 0);
            const inULeftLeg = (x >= -(R + H) && x <= -H && z >= 0 && z <= R);
            const inURightLeg = (x >= H && x <= (R + H) && z >= 0 && z <= R);
            return inUTopBar || inULeftLeg || inURightLeg;

        case 'CROSS_SHAPE':
            // Cross shape: horizontal bar (3x1) and vertical bar (1x3) intersecting at center
            // Horizontal: (-R-H to R+H, -H to H)
            // Vertical: (-H to H, -R-H to R+H)
            const inHorizontalBar = (x >= -(R + H) && x <= (R + H) && z >= -H && z <= H);
            const inVerticalBar = (x >= -H && x <= H && z >= -(R + H) && z <= (R + H));
            return inHorizontalBar || inVerticalBar;

        case 'BOSS_ARENA':
            // Boss arena is typically much larger - use bounding box validation
            return true;

        default:
            console.warn(`[RoomShapeValidation] Unknown room shape: ${roomShape}, allowing position`);
            return true;
    }
}

// --- Object Placement Details ---
// These define specific placement strategies for objects
const PLACEMENT_DETAILS = {
    random: (room, objectSize, index, totalCount, roomShape) => {
        // CRITICAL FIX: Generate random positions that respect room shape
        let attempts = 0;
        const maxAttempts = 50;

        while (attempts < maxAttempts) {
            const x = (Math.random() - 0.5) * (room.width - objectSize * 2);
            const z = (Math.random() - 0.5) * (room.depth - objectSize * 2);

            // Validate position against room shape
            if (_isPositionInRoomShape(x, z, roomShape)) {
                return { x, y: 0, z };
            }
            attempts++;
        }

        // If we couldn't find a valid position, return center (should be valid for most shapes)
        console.warn(`[EnvironmentObjectManager] Could not find valid random position for room shape ${roomShape}, using center`);
        return { x: 0, y: 0, z: 0 };
    },
    corners: (room, objectSize, index, totalCount, roomShape) => {
        // CRITICAL FIX: Place objects in valid corners only
        const allCorners = [
            { x: -1, z: -1, name: 'SW' }, // Southwest (bottom-left)
            { x: 1, z: -1, name: 'SE' },  // Southeast (bottom-right)
            { x: -1, z: 1, name: 'NW' },  // Northwest (top-left)
            { x: 1, z: 1, name: 'NE' }    // Northeast (top-right)
        ];

        // Filter corners to only include those valid for the room shape
        const validCorners = allCorners.filter(corner => {
            const offset = objectSize * 1.5;
            const x = corner.x * (room.width / 2 - offset);
            const z = corner.z * (room.depth / 2 - offset);
            return _isPositionInRoomShape(x, z, roomShape);
        });

        if (validCorners.length === 0) {
            console.warn(`[EnvironmentObjectManager] No valid corners found for room shape ${roomShape}, using center`);
            return { x: 0, y: 0, z: 0 };
        }

        // Use modulo to cycle through valid corners
        const cornerIndex = index % validCorners.length;
        const selectedCorner = validCorners[cornerIndex];
        const offset = objectSize * 1.5;
        const x = selectedCorner.x * (room.width / 2 - offset);
        const z = selectedCorner.z * (room.depth / 2 - offset);

        console.log(`[EnvironmentObjectManager] Placing corner object ${index} in ${selectedCorner.name} corner at (${x.toFixed(2)}, ${z.toFixed(2)}) for room shape ${roomShape}`);
        return { x, y: 0, z };
    },
    center: (room) => {
        // Place object in the center of the room
        return { x: 0, y: 0, z: 0 };
    },
    walls: (room, objectSize, index, totalCount, roomShape) => {
        // CRITICAL FIX: Distribute objects evenly along walls with room shape validation
        // Skip the invisible south wall (index 2)
        // 0: north, 1: east, 3: west
        let wallIndex = index % 3;
        if (wallIndex === 2) wallIndex = 3; // Map 2 to 3 (west) to skip south wall

        const positionAlongWall = (index / totalCount) * 2 - 1; // -1 to 1

        let x = 0, z = 0;
        let wallName = '';
        let inwardNormal = new THREE.Vector3(); // Normal pointing into the room

        switch (wallIndex) {
            case 0: // North wall
                x = positionAlongWall * (room.width / 2 - objectSize);
                z = -room.depth / 2 + objectSize / 2;
                wallName = 'North';
                inwardNormal.set(0, 0, 1); // Facing south (into room)
                break;
            case 1: // East wall
                x = room.width / 2 - objectSize / 2;
                z = positionAlongWall * (room.depth / 2 - objectSize);
                wallName = 'East';
                inwardNormal.set(-1, 0, 0); // Facing west (into room)
                break;
            case 3: // West wall (skipping South wall)
                x = -room.width / 2 + objectSize / 2;
                z = positionAlongWall * (room.depth / 2 - objectSize);
                wallName = 'West';
                inwardNormal.set(1, 0, 0); // Facing east (into room)
                break;
        }

        // CRITICAL FIX: Validate wall position against room shape
        if (!_isPositionInRoomShape(x, z, roomShape)) {
            console.warn(`[EnvironmentObjectManager] Wall position (${x.toFixed(2)}, ${z.toFixed(2)}) invalid for room shape ${roomShape}, trying alternative walls`);

            // ENHANCED FIX: Try alternative wall positions instead of center
            const alternativeWalls = [
                { index: 0, name: 'North' }, // North wall
                { index: 1, name: 'East' },  // East wall
                { index: 3, name: 'West' }   // West wall (skip South which is invisible)
            ];

            for (const altWall of alternativeWalls) {
                if (altWall.index === wallIndex) continue; // Skip the wall we already tried

                let altX = 0, altZ = 0;
                let altInwardNormal = new THREE.Vector3();

                switch (altWall.index) {
                    case 0: // North wall
                        altX = positionAlongWall * (room.width / 2 - objectSize);
                        altZ = -room.depth / 2 + objectSize / 2;
                        altInwardNormal.set(0, 0, 1);
                        break;
                    case 1: // East wall
                        altX = room.width / 2 - objectSize / 2;
                        altZ = positionAlongWall * (room.depth / 2 - objectSize);
                        altInwardNormal.set(-1, 0, 0);
                        break;
                    case 3: // West wall
                        altX = -room.width / 2 + objectSize / 2;
                        altZ = positionAlongWall * (room.depth / 2 - objectSize);
                        altInwardNormal.set(1, 0, 0);
                        break;
                }

                // Check if this alternative wall position is valid
                if (_isPositionInRoomShape(altX, altZ, roomShape)) {
                    console.log(`[EnvironmentObjectManager] Found valid alternative wall position on ${altWall.name} wall: (${altX.toFixed(2)}, ${altZ.toFixed(2)})`);
                    return { x: altX, y: 0, z: altZ, wallIndex: altWall.index, wallName: altWall.name, inwardNormal: altInwardNormal };
                }
            }

            // If no alternative walls work, return null to skip this object
            console.warn(`[EnvironmentObjectManager] No valid wall positions found for room shape ${roomShape}, skipping wall object`);
            return null;
        }

        return { x, y: 0, z, wallIndex, wallName, inwardNormal };
    },

    // Alias for 'walls' for compatibility
    wall: (room, objectSize, index, totalCount, roomShape) => {
        return PLACEMENT_DETAILS.walls(room, objectSize, index, totalCount, roomShape);
    }
};

class EnvironmentObjectManager {
    constructor() {
        // Cache for biome object pools
        this.biomePoolCache = {};

        // Debug mode
        this.debugMode = false;
    }

    /**
     * Enable or disable debug mode
     * @param {boolean} enabled - Whether debug mode should be enabled
     */
    setDebugMode(enabled) {
        this.debugMode = enabled;
    }

    /**
     * Get the object pool for a specific biome
     * @param {string} biome - The biome to get the object pool for
     * @returns {object} The object pool for the biome
     */
    getObjectPool(biome) {
        // Check if we already have this biome's pool cached
        if (this.biomePoolCache[biome]) {
            return this.biomePoolCache[biome];
        }

        // Get the base object pool for this biome
        const biomePool = BIOME_OBJECT_POOLS[biome] || BIOME_OBJECT_POOLS.catacombs;

        // Cache it
        this.biomePoolCache[biome] = biomePool;

        return biomePool;
    }

    /**
     * Choose a balanced object group for a room
     * @param {object} context - The context for object placement
     * @param {string} context.biome - The current biome
     * @param {string} context.roomType - The type of room
     * @param {object} context.roomData - The room data
     * @param {number} context.seed - Random seed for deterministic placement
     * @returns {Array} Array of objects to place
     */
    chooseObjectGroup(context) {
        const { biome, roomType, roomData, seed = Date.now() } = context;

        // Create seeded random function
        const random = mulberry32(seed);

        // Get the object pool for this biome
        const objectPool = this.getObjectPool(biome);

        // Determine which template group to use based on room type
        let templateGroup;
        switch (roomType.toLowerCase()) {
            case 'start':
                templateGroup = OBJECT_GROUP_TEMPLATES.start;
                break;
            case 'boss':
                templateGroup = OBJECT_GROUP_TEMPLATES.boss;
                break;
            case 'elite':
                templateGroup = OBJECT_GROUP_TEMPLATES.elite;
                break;
            default:
                templateGroup = OBJECT_GROUP_TEMPLATES.normal;
        }

        // Choose a template based on probability
        let template = null;
        const roll = random();
        let cumulativeProbability = 0;

        for (const t of templateGroup) {
            cumulativeProbability += t.probability;
            if (roll < cumulativeProbability) {
                template = t;
                break;
            }
        }

        // If no template was selected, use the first one
        if (!template && templateGroup.length > 0) {
            template = templateGroup[0];
        }

        if (!template) {
            console.warn(`No object template found for room type: ${roomType}`);
            return [];
        }

        // Generate objects based on the template
        const objectsToPlace = [];

        console.log(`[EnvironmentObjectManager] Using template:`, JSON.stringify(template, null, 2));

        for (const role of template.roles) {
            const { type, placement, count, placementDetail } = role;

            console.log(`[EnvironmentObjectManager] Processing role: type=${type}, placement=${placement}, count=${count}`);

            // Get available objects for this role and placement
            const availableObjects = objectPool[type]?.[placement] || [];

            console.log(`[EnvironmentObjectManager] Available objects for ${type}/${placement}:`, availableObjects);

            if (availableObjects.length === 0) {
                console.warn(`[EnvironmentObjectManager] No objects available for role ${type} and placement ${placement} in biome ${biome}`);
                continue;
            }

            // Place the specified number of objects
            for (let i = 0; i < count; i++) {
                // Choose a random object from the available ones
                const objectIndex = Math.floor(random() * availableObjects.length);
                const objectType = availableObjects[objectIndex];

                // For wall objects, use the 'wall' placement detail if not specified
                let effectivePlacementDetail = placementDetail || 'random';
                if (placement === 'wall' && !placementDetail) {
                    effectivePlacementDetail = 'wall';
                    console.log(`[EnvironmentObjectManager] Using 'wall' placement detail for ${objectType}`);
                }

                objectsToPlace.push({
                    type: objectType,
                    placement,
                    placementDetail: effectivePlacementDetail,
                    index: i,
                    totalCount: count
                });
            }
        }

        if (this.debugMode) {
            console.log(`Chose object group for ${biome} (${roomType}):`);
            console.log(`Objects to place: ${objectsToPlace.map(o => o.type).join(', ')}`);
        }

        return objectsToPlace;
    }

    /**
     * Get placement positions for objects in a room
     * @param {Array} objectsToPlace - Array of objects to place
     * @param {object} roomDimensions - The dimensions of the room
     * @param {number} seed - Random seed for deterministic placement
     * @param {string} roomShape - The shape of the room for validation
     * @returns {Array} Array of objects with position information
     */
    getObjectPlacements(objectsToPlace, roomDimensions, seed = Date.now(), roomShape = null) {
        console.log(`[EnvironmentObjectManager] Getting placements for ${objectsToPlace.length} objects`);

        // Create seeded random function
        const random = mulberry32(seed);

        // Default object size
        const defaultObjectSize = 1.0;

        // Process each object and filter out null results
        return objectsToPlace.map(object => {
            const { type, placement, placementDetail, index, totalCount } = object;

            console.log(`[EnvironmentObjectManager] Placing object: type=${type}, placement=${placement}, detail=${placementDetail}`);

            // Get placement function based on placement detail
            const placementFunc = PLACEMENT_DETAILS[placementDetail] || PLACEMENT_DETAILS.random;

            // Calculate position
            console.log(`[EnvironmentObjectManager] Using placement function: ${placementDetail || 'random'} for room shape: ${roomShape || 'unknown'}`);
            const placementResult = placementFunc(roomDimensions, defaultObjectSize, index, totalCount, roomShape);

            // CRITICAL FIX: Handle null placement result (when no valid position found)
            if (placementResult === null) {
                console.warn(`[EnvironmentObjectManager] Placement function returned null for ${type}, skipping object`);
                return null; // Skip this object
            }

            // Extract position and additional wall info if available
            let position;
            let wallInfo = {};

            if (typeof placementResult === 'object' && 'x' in placementResult && 'y' in placementResult && 'z' in placementResult) {
                // Extract position
                position = {
                    x: placementResult.x,
                    y: placementResult.y,
                    z: placementResult.z
                };

                // Extract wall info if available
                if ('inwardNormal' in placementResult) {
                    wallInfo.inwardNormal = placementResult.inwardNormal;
                }
                if ('wallName' in placementResult) {
                    wallInfo.wallName = placementResult.wallName;
                }
                if ('wallIndex' in placementResult) {
                    wallInfo.wallIndex = placementResult.wallIndex;
                }
            } else {
                // Legacy support for functions that just return position
                position = placementResult;
            }

            console.log(`[EnvironmentObjectManager] Initial position: x=${position.x}, y=${position.y}, z=${position.z}`);
            if (Object.keys(wallInfo).length > 0) {
                console.log(`[EnvironmentObjectManager] Wall info:`, wallInfo);
            }

            // Add some randomness to position if not using specific placement
            if (placementDetail === 'random') {
                position.x += (random() - 0.5) * 0.5;
                position.z += (random() - 0.5) * 0.5;
                console.log(`[EnvironmentObjectManager] Added randomness: x=${position.x}, y=${position.y}, z=${position.z}`);
            }

            // Add rotation
            const rotation = random() * Math.PI * 2;

            return {
                ...object,
                position,
                rotation,
                options: {
                    ...object,
                    position,
                    rotation,
                    placement: object.placement,
                    ...wallInfo  // Include wall info in options
                }
            };
        }).filter(Boolean); // CRITICAL FIX: Remove null results from failed placements
    }

    /**
     * Create object instances for placement in a room
     * @param {Array} objectPlacements - Array of objects with position information
     * @returns {Array} Array of object instances
     */
    createObjectInstances(objectPlacements) {
        console.log('Creating object instances for', objectPlacements.length, 'objects');

        return objectPlacements.map(object => {
            const { type, placement, position, rotation } = object;

            // Get the prefab function for this object type
            const prefabFunc = getPrefabFunction(type, 'interior');

            if (!prefabFunc) {
                console.warn(`No prefab function found for object type: ${type}`);
                return null;
            }

            console.log(`Found prefab function for ${type}:`, prefabFunc ? 'YES' : 'NO');

            // Create options for the prefab function
            const isDestructible = OBJECT_ROLE_MAP[type] === OBJECT_ROLES.DESTRUCTIBLE;
            console.log(`Object ${type} isDestructible: ${isDestructible}, role: ${OBJECT_ROLE_MAP[type]}`);

            // Get any additional options from the object
            const additionalOptions = object.options || {};

            const options = {
                position,
                rotation,
                placement,
                isDestructible: isDestructible,
                destructionEffect: 'collapse',
                health: 1,
                ...additionalOptions // Include any additional options like inwardNormal, wallName, etc.
            };

            return {
                type,
                prefabFunc,
                options
            };
        }).filter(Boolean);
    }
}

// Create and export a singleton instance
const environmentObjectManager = new EnvironmentObjectManager();
export default environmentObjectManager;
