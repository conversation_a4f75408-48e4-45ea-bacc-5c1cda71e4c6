import * as THREE from 'three';
import * as BufferGeometryUtils from 'three/addons/utils/BufferGeometryUtils.js';

// Imports from shared prefabs
import {
    VOXEL_SIZE,
    ENVIRONMENT_PIXEL_SCALE,
    mulberry32
} from '../generators/prefabs/shared.js';

// --- Import prefab functions ---
import { getPrefabFunction } from '../prefabs/prefabs.js';

// --- Import environment systems ---
import environmentObjectManager from '../systems/EnvironmentObjectManager.js';
import environmentTypeManager from '../systems/EnvironmentTypeManager.js';

// --- NEW: Import pillar and rubble directly ---
import { createStonePillarObject } from '../generators/prefabs/stonePillarObject.js';
import { createStoneRubbleObject } from '../generators/prefabs/stoneRubbleObject.js';
import { createRitualCircleObject } from '../generators/prefabs/ritualCircleObject.js'; // <-- Import Ritual Circle
import { createAetherTorchObject } from '../generators/prefabs/aetherTorchObject.js'; // <-- Import Aether Torch

// --- Import Effects ---
// import { createFloorFogParticles } from '../effects/FloorFog.js'; // Remove old import
import { createFloorMistPlanes } from '../effects/FloorMistPlane.js'; // Add new import

// --- Constants for room generation ---
export const ROOM_WORLD_SIZE = 14; // Size of a standard room in world units
export const WALL_HEIGHT = 4.0;   // Standard wall height
export const WALL_DEPTH = 0.5;    // Standard wall thickness
export const DOOR_WIDTH = 2.5;    // Standard door width
export const DARK_ROOM_PROBABILITY = 0.0; // Probability of a room being dark (0.0 = never)

// Helper function to get object radius for collision detection
function getObjectRadius(objectType) {
    switch (objectType) {
        // Environment objects
        case 'stone_pillar': return VOXEL_SIZE * 3;
        case 'stone_vase': return VOXEL_SIZE * 1.5;
        case 'stone_rubble': return VOXEL_SIZE * 2.5;
        case 'ritual_circle': return VOXEL_SIZE * 5;
        case 'aether_torch': return VOXEL_SIZE * 1.5;

        // Other objects
        case 'large_chest': return VOXEL_SIZE * 3;
        case 'table': return VOXEL_SIZE * 2.5;
        case 'torch': return VOXEL_SIZE * 6; // Increased for larger minimum distance
        case 'vine': return VOXEL_SIZE * 0.5; // Very small radius for vines

        // Default
        default: return DEFAULT_OBJECT_RADIUS;
    }
}

// --- NEW: Constants for Object Placement ---
const MIN_DISTANCE_FROM_DOOR = DOOR_WIDTH; // Minimum distance object center can be from door center
const DEFAULT_OBJECT_RADIUS = VOXEL_SIZE * 2; // Default approximate radius for collision checks
const MAX_PLACEMENT_ATTEMPTS = 20; // Max attempts to find a spot for an object
const DOOR_HEIGHT = WALL_HEIGHT * 0.9;



// --- NEW: Room Area Thresholds for Torch Scaling ---
const SMALL_AREA_THRESHOLD = 14 * 14;  // 196 (14x14)
const MEDIUM_AREA_THRESHOLD = 28 * 14; // 392 (28x14)

// These constants are already declared above

// --- Helper function to add floor segments ---
// Accepts the specific floor prefab FUNCTION
export function addFloorSegment(roomGroup, collisionMeshes, width, depth, position, roomData, floorPrefabFunc) {
    if (!floorPrefabFunc) { // Check if function was provided
        console.error("[addFloorSegment] Missing floorPrefabFunc. Skipping floor.");
        return;
    }
    // const floorMesh = themeData.floorPrefab(width, depth, roomData); // OLD
    const floorMesh = floorPrefabFunc(width, depth, roomData); // NEW - Call passed function
    if (floorMesh) {
        floorMesh.position.copy(position);
        roomGroup.add(floorMesh);

        const addColliders = (mesh) => {
            if (mesh.isGroup) {
                mesh.children.forEach(child => {
                    if (child.isMesh) {
                        child.receiveShadow = true;
                        // Mark as floor for collision detection
                        if (!child.userData) child.userData = {};
                        child.userData.isFloor = true;
                        collisionMeshes.push(child);
                    }
                });
            } else if (mesh.isMesh) {
                mesh.receiveShadow = true;
                // Mark as floor for collision detection
                if (!mesh.userData) mesh.userData = {};
                mesh.userData.isFloor = true;
                collisionMeshes.push(mesh);
            }
        };
        addColliders(floorMesh);
    }
}

// --- Helper to create a single torch light instance ---
function _createTorchLight() {
    // Significantly increased brightness and range for better illumination
    const pointLight = new THREE.PointLight(0xffbb60, 1.5, 12.0);
    pointLight.castShadow = false;
    pointLight.name = "torchLight";
    pointLight.userData = { baseIntensity: 1.5, baseRange: 12.0 };
    return pointLight;
}

// --- Helper function to add wall segments ---
// Accepts the specific wall prefab FUNCTION
export function addWallSegment(roomGroup, collisionMeshes, lights, segmentWidth, position, rotationY, isOuterBoundary, isDarkRoom, roomData, roomMaxZ, wallPrefabFunc) {
    if (!segmentWidth || typeof segmentWidth !== 'number' || segmentWidth <= 0 || !position || !roomData) {
        console.error(`[addWallSegment] Invalid parameters received: segmentWidth=${segmentWidth}. Skipping.`);
        return;
    }
    // if (!themeData || !themeData.wallPrefab) { // OLD Check
    if (!wallPrefabFunc) { // NEW Check
        console.error("[addWallSegment] Missing wallPrefabFunc. Skipping wall segment.");
        return;
    }

    // const wallData = themeData.wallPrefab(...); // OLD
    const wallData = wallPrefabFunc(segmentWidth, WALL_HEIGHT, WALL_DEPTH, roomData, isDarkRoom, position, rotationY); // NEW

    if (!wallData || !wallData.group) {
         console.error(`[addWallSegment] Wall prefab function failed for segmentWidth=${segmentWidth}.`);
         return;
    }

    const wallSegmentGroup = wallData.group;
    wallSegmentGroup.position.copy(position);
    wallSegmentGroup.rotation.y = rotationY;
    wallSegmentGroup.traverse(child => { if (child.isMesh) child.castShadow = true; });

    wallSegmentGroup.userData.isWallSegment = true;
    wallSegmentGroup.userData.width = segmentWidth; // Store width for potential use (e.g., decorations)

    let isVisible = true;
    const epsilon = 0.1;
    if (position.z >= roomMaxZ - epsilon) {
        isVisible = false;
    }
    wallSegmentGroup.visible = isVisible;

    roomGroup.add(wallSegmentGroup);

    if (wallSegmentGroup.visible) {
         if (wallSegmentGroup.isMesh) {
            collisionMeshes.push(wallSegmentGroup);
         } else {
             wallSegmentGroup.traverse(child => {
                if (child.isMesh) {
                    collisionMeshes.push(child);
                }
            });
         }
        (wallData.torchPositions || []).forEach(localPos => { // Add default empty array
            const torchLight = _createTorchLight();
            const worldPos = wallSegmentGroup.localToWorld(localPos.clone());
            torchLight.position.copy(worldPos);
            lights.push(torchLight);
        });
    }
}

// --- Function to add door/archway prefab ---
// Accepts the specific door prefab FUNCTION
export function addDoorPrefab(roomGroup, collisionMeshes, position, rotationY, roomData, doorPrefabFunc) {
    // if (!themeData) { // OLD Check
    if (!doorPrefabFunc) { // NEW Check
        console.warn(`[addDoorPrefab] Missing doorPrefabFunc for room ${roomData.id}. Skipping door.`);
        return null;
    }

    // const doorGroup = themeData.doorPrefab(DOOR_WIDTH, DOOR_HEIGHT, WALL_DEPTH); // OLD
    const doorGroup = doorPrefabFunc(DOOR_WIDTH, DOOR_HEIGHT, WALL_DEPTH); // NEW

    if (doorGroup) {
        // CRITICAL FIX: Position door properly accounting for wall thickness
        // Doors should be flush with the wall surface, not floating in front
        doorGroup.position.set(position.x, 0, position.z);
        doorGroup.rotation.y = rotationY;

        // Apply slight inward offset to ensure door is properly embedded in wall
        const doorInwardOffset = WALL_DEPTH * 0.1; // Small offset to prevent z-fighting
        const inwardDirection = new THREE.Vector3(0, 0, 1);
        inwardDirection.applyAxisAngle(new THREE.Vector3(0, 1, 0), rotationY);
        doorGroup.position.addScaledVector(inwardDirection, doorInwardOffset);
        doorGroup.traverse(child => {
            if (child.isMesh) {
                child.castShadow = true;
                collisionMeshes.push(child);
            }
        });
        doorGroup.userData.isDoor = true;
        return doorGroup;
    } else {
        console.error(`[addDoorPrefab] Door prefab function failed (returned falsy value) for room ${roomData.id}`);
        return null;
    }
}

// --- Function to add wall decorations --- (Needs update if used)
// export function addWallDecorations(roomGroup, roomData, themeData, isDarkRoom) { // Keep old signature for now
//     // TODO: This function also needs refactoring if wall decorations are defined in areas.js
//     // It would need to read decoration keys from areaData, map them to prefab functions, etc.
//     // For now, it might be unused or rely on the old themeData structure if called elsewhere.
// }

// --- Helper to define wall AND floor segments based on room shape ---
function _getRoomGeometrySegments(shapeKey, roomWidth, roomDepth, halfWidth, halfDepth, connections) {
    let wallSegments = [];
    let floorSegments = [];
    const R = ROOM_WORLD_SIZE; // e.g., 14
    const H = R / 2;       // e.g., 7

    switch (shapeKey) {
        case 'RECTANGULAR':
        case 'RECT_2X1':
            // Walls
            wallSegments.push({ position: new THREE.Vector3(0, WALL_HEIGHT / 2, -halfDepth), rotation: 0, length: roomWidth, isOuterBoundary: true, direction: 'n' }); // N
            wallSegments.push({ position: new THREE.Vector3(halfWidth, WALL_HEIGHT / 2, 0), rotation: Math.PI / 2, length: roomDepth, isOuterBoundary: true, direction: 'e' }); // E
            wallSegments.push({ position: new THREE.Vector3(0, WALL_HEIGHT / 2, halfDepth), rotation: Math.PI, length: roomWidth, isOuterBoundary: true, direction: 's' }); // S
            wallSegments.push({ position: new THREE.Vector3(-halfWidth, WALL_HEIGHT / 2, 0), rotation: -Math.PI / 2, length: roomDepth, isOuterBoundary: true, direction: 'w' }); // W
            // Floor (Single piece)
            floorSegments.push({ position: new THREE.Vector3(0, 0, 0), width: roomWidth, depth: roomDepth });
            break;

        case 'L_SHAPE':
            // L-Shape: A room in the shape of an L
            // The L is positioned with the vertical part on the left and horizontal part on the bottom
            wallSegments = [
                // North walls
                // Top of the vertical part of the L
                { position: new THREE.Vector3(-H, WALL_HEIGHT / 2, -R), rotation: 0, length: R, isOuterBoundary: true, direction: 'n' },

                // East walls
                // Right side of the horizontal part
                { position: new THREE.Vector3(R, WALL_HEIGHT / 2, H), rotation: Math.PI / 2, length: R, isOuterBoundary: true, direction: 'e' },

                // South walls
                // Bottom of the horizontal part
                { position: new THREE.Vector3(0, WALL_HEIGHT / 2, R), rotation: Math.PI, length: 2*R, isOuterBoundary: true, direction: 's' },

                // West walls
                // Left side of the vertical part
                { position: new THREE.Vector3(-R, WALL_HEIGHT / 2, -H), rotation: -Math.PI / 2, length: R, isOuterBoundary: true, direction: 'w' },
                // Left side of the horizontal part
                { position: new THREE.Vector3(-R, WALL_HEIGHT / 2, H), rotation: -Math.PI / 2, length: R, isOuterBoundary: true, direction: 'w' },

                // Inner corner walls
                // Top wall of the horizontal part (connecting to vertical part)
                { position: new THREE.Vector3(H, WALL_HEIGHT / 2, 0), rotation: 0, length: R, isOuterBoundary: true, direction: 'n' },
                // Right wall of the vertical part (connecting to horizontal part)
                { position: new THREE.Vector3(0, WALL_HEIGHT / 2, -H), rotation: Math.PI / 2, length: R, isOuterBoundary: true, direction: 'e' }
            ];

            // Floor segments - create the L shape with two rectangles
            // Vertical part (left side)
            floorSegments.push({ position: new THREE.Vector3(-H, 0, -H), width: R, depth: R });
            // Horizontal part (bottom)
            floorSegments.push({ position: new THREE.Vector3(0, 0, H), width: 2*R, depth: R });
            break;

        case 'T_SHAPE':
            // Redefined based on floor perimeter (Origin at junction)
            // Corrected: Mark inner bottom edges of top bar as South walls
            wallSegments = [
                // Outer Perimeter (Remain the same)
                { position: new THREE.Vector3(0, WALL_HEIGHT / 2, -R), rotation: 0, length: 3*R, isOuterBoundary: true, direction: 'n' }, // 1. Top of bar
                { position: new THREE.Vector3(R+H, WALL_HEIGHT / 2, -H), rotation: Math.PI / 2, length: R, isOuterBoundary: true, direction: 'e' },// 2. Right of top-right square
                { position: new THREE.Vector3(H, WALL_HEIGHT / 2, H), rotation: Math.PI / 2, length: R, isOuterBoundary: true, direction: 'e' },    // 3. Right of stem square
                { position: new THREE.Vector3(0, WALL_HEIGHT / 2, R), rotation: Math.PI, length: R, isOuterBoundary: true, direction: 's' },    // 4. Bottom of stem square
                { position: new THREE.Vector3(-H, WALL_HEIGHT / 2, H), rotation: -Math.PI / 2, length: R, isOuterBoundary: true, direction: 'w' },   // 5. Left of stem square
                { position: new THREE.Vector3(-(R+H), WALL_HEIGHT / 2, -H), rotation: -Math.PI / 2, length: R, isOuterBoundary: true, direction: 'w' },// 6. Left of top-left square
                // Inner Walls (Now treated as potential connection points)
                { position: new THREE.Vector3(R, WALL_HEIGHT / 2, 0), rotation: Math.PI, length: R, isOuterBoundary: true, direction: 's' },  // 7. Bottom of top-right square (South)
                { position: new THREE.Vector3(-R, WALL_HEIGHT / 2, 0), rotation: Math.PI, length: R, isOuterBoundary: true, direction: 's' }   // 8. Bottom of top-left square (South)
            ];
            // Floor (Remains the same)
            floorSegments.push({ position: new THREE.Vector3(0, 0, -H), width: 3*R, depth: R }); // Top bar (3x1). Z spans [-R, 0]
            floorSegments.push({ position: new THREE.Vector3(0, 0, H), width: R, depth: R }); // Stem (1x1). Z spans [0, R]
            break;

        case 'CROSS_SHAPE':
             // Corrected: Mark all inner walls as potential outer boundaries with directions
            wallSegments = [
                // Outer Walls (Remain the same)
                { position: new THREE.Vector3(0, WALL_HEIGHT / 2, -(R+H)), rotation: 0, length: R, isOuterBoundary: true, direction: 'n' }, // 1. Top face
                { position: new THREE.Vector3(R+H, WALL_HEIGHT / 2, 0), rotation: Math.PI / 2, length: R, isOuterBoundary: true, direction: 'e' },// 4. Right face
                { position: new THREE.Vector3(0, WALL_HEIGHT / 2, R+H), rotation: Math.PI, length: R, isOuterBoundary: true, direction: 's' },   // 7. Bottom face
                { position: new THREE.Vector3(-(R+H), WALL_HEIGHT / 2, 0), rotation: -Math.PI / 2, length: R, isOuterBoundary: true, direction: 'w' },// 10. Left face
                // Inner Walls (Now treated as potential connection points)
                { position: new THREE.Vector3(H, WALL_HEIGHT / 2, -R), rotation: Math.PI / 2, length: R, isOuterBoundary: true, direction: 'e' },// 2. Top-right inner vertical (East)
                { position: new THREE.Vector3(R, WALL_HEIGHT / 2, -H), rotation: 0, length: R, isOuterBoundary: true, direction: 'n' },      // 3. Top-right inner horizontal (North)
                { position: new THREE.Vector3(R, WALL_HEIGHT / 2, H), rotation: Math.PI, length: R, isOuterBoundary: true, direction: 's' },   // 5. Bottom-right inner horizontal (South)
                { position: new THREE.Vector3(H, WALL_HEIGHT / 2, R), rotation: -Math.PI / 2, length: R, isOuterBoundary: true, direction: 'w' },// 6. Bottom-right inner vertical (West)
                { position: new THREE.Vector3(-H, WALL_HEIGHT / 2, R), rotation: Math.PI / 2, length: R, isOuterBoundary: true, direction: 'e' },// 8. Bottom-left inner vertical (East)
                { position: new THREE.Vector3(-R, WALL_HEIGHT / 2, H), rotation: Math.PI, length: R, isOuterBoundary: true, direction: 's' },  // 9. Bottom-left inner horizontal (South)
                { position: new THREE.Vector3(-R, WALL_HEIGHT / 2, -H), rotation: 0, length: R, isOuterBoundary: true, direction: 'n' },     // 11. Top-left inner horizontal (North)
                { position: new THREE.Vector3(-H, WALL_HEIGHT / 2, -R), rotation: -Math.PI / 2, length: R, isOuterBoundary: true, direction: 'w' }// 12. Top-left inner vertical (West)
            ];
            // Floor (Remains the same)
            floorSegments.push({ position: new THREE.Vector3(0, 0, 0), width: R, depth: R });   // Center
            floorSegments.push({ position: new THREE.Vector3(0, 0, -R), width: R, depth: R });  // Top
            floorSegments.push({ position: new THREE.Vector3(0, 0, R), width: R, depth: R });   // Bottom
            floorSegments.push({ position: new THREE.Vector3(-R, 0, 0), width: R, depth: R }); // Left
            floorSegments.push({ position: new THREE.Vector3(R, 0, 0), width: R, depth: R });  // Right
            break;

        case 'BOSS_ARENA':
            // A large square arena for boss fights (similar to SQUARE_2X2 but larger)
            const B = 2 * R; // Boss room size multiplier (2x normal room size)

            // Walls - Following the same pattern as SQUARE_2X2 and other rectangular rooms
            wallSegments = [
                // North wall (top)
                { position: new THREE.Vector3(0, WALL_HEIGHT / 2, -B), rotation: 0, length: 2*B, isOuterBoundary: true, direction: 'n' },
                // East wall (right)
                { position: new THREE.Vector3(B, WALL_HEIGHT / 2, 0), rotation: Math.PI / 2, length: 2*B, isOuterBoundary: true, direction: 'e' },
                // South wall (bottom)
                { position: new THREE.Vector3(0, WALL_HEIGHT / 2, B), rotation: Math.PI, length: 2*B, isOuterBoundary: true, direction: 's' },
                // West wall (left)
                { position: new THREE.Vector3(-B, WALL_HEIGHT / 2, 0), rotation: -Math.PI / 2, length: 2*B, isOuterBoundary: true, direction: 'w' }
            ];

            // Floor (Single large piece, similar to SQUARE_2X2)
            floorSegments = [
                { position: new THREE.Vector3(0, 0, 0), width: 2*B, depth: 2*B }
            ];
            break;

        case 'SQUARE_2X2':
            // Walls (Standard 4 walls for 2Rx2R)
            wallSegments.push({ position: new THREE.Vector3(0, WALL_HEIGHT / 2, -R), rotation: 0, length: 2*R, isOuterBoundary: true, direction: 'n' }); // N
            wallSegments.push({ position: new THREE.Vector3(R, WALL_HEIGHT / 2, 0), rotation: Math.PI / 2, length: 2*R, isOuterBoundary: true, direction: 'e' }); // E
            wallSegments.push({ position: new THREE.Vector3(0, WALL_HEIGHT / 2, R), rotation: Math.PI, length: 2*R, isOuterBoundary: true, direction: 's' }); // S
            wallSegments.push({ position: new THREE.Vector3(-R, WALL_HEIGHT / 2, 0), rotation: -Math.PI / 2, length: 2*R, isOuterBoundary: true, direction: 'w' }); // W
            // Floor (Single 2Rx2R piece)
            floorSegments.push({ position: new THREE.Vector3(0, 0, 0), width: 2*R, depth: 2*R });
            break;

        case 'RECT_3X1':
            // Walls (Standard 4 walls for 3Rx1R)
            wallSegments.push({ position: new THREE.Vector3(0, WALL_HEIGHT / 2, -H), rotation: 0, length: 3*R, isOuterBoundary: true, direction: 'n' }); // N
            wallSegments.push({ position: new THREE.Vector3(R+H, WALL_HEIGHT / 2, 0), rotation: Math.PI / 2, length: R, isOuterBoundary: true, direction: 'e' }); // E
            wallSegments.push({ position: new THREE.Vector3(0, WALL_HEIGHT / 2, H), rotation: Math.PI, length: 3*R, isOuterBoundary: true, direction: 's' }); // S
            wallSegments.push({ position: new THREE.Vector3(-(R+H), WALL_HEIGHT / 2, 0), rotation: -Math.PI / 2, length: R, isOuterBoundary: true, direction: 'w' }); // W
            // Floor (Single 3Rx1R piece)
            floorSegments.push({ position: new THREE.Vector3(0, 0, 0), width: 3*R, depth: R });
            break;

        case 'RECT_1X2':
             // Walls (Standard 4 walls for 1Rx2R)
            wallSegments.push({ position: new THREE.Vector3(0, WALL_HEIGHT / 2, -R), rotation: 0, length: R, isOuterBoundary: true, direction: 'n' }); // N
            wallSegments.push({ position: new THREE.Vector3(H, WALL_HEIGHT / 2, 0), rotation: Math.PI / 2, length: 2*R, isOuterBoundary: true, direction: 'e' }); // E
            wallSegments.push({ position: new THREE.Vector3(0, WALL_HEIGHT / 2, R), rotation: Math.PI, length: R, isOuterBoundary: true, direction: 's' }); // S
            wallSegments.push({ position: new THREE.Vector3(-H, WALL_HEIGHT / 2, 0), rotation: -Math.PI / 2, length: 2*R, isOuterBoundary: true, direction: 'w' }); // W
            // Floor (Single 1Rx2R piece)
            floorSegments.push({ position: new THREE.Vector3(0, 0, 0), width: R, depth: 2*R });
            break;

        case 'RECT_1X3':
            // Walls (Standard 4 walls for 1Rx3R)
            wallSegments.push({ position: new THREE.Vector3(0, WALL_HEIGHT / 2, -(R+H)), rotation: 0, length: R, isOuterBoundary: true, direction: 'n' }); // N
            wallSegments.push({ position: new THREE.Vector3(H, WALL_HEIGHT / 2, 0), rotation: Math.PI / 2, length: 3*R, isOuterBoundary: true, direction: 'e' }); // E
            wallSegments.push({ position: new THREE.Vector3(0, WALL_HEIGHT / 2, R+H), rotation: Math.PI, length: R, isOuterBoundary: true, direction: 's' }); // S
            wallSegments.push({ position: new THREE.Vector3(-H, WALL_HEIGHT / 2, 0), rotation: -Math.PI / 2, length: 3*R, isOuterBoundary: true, direction: 'w' }); // W
            // Floor (Single 1Rx3R piece)
            floorSegments.push({ position: new THREE.Vector3(0, 0, 0), width: R, depth: 3*R });
            break;

        case 'RECT_3X2':
            // Walls (Standard 4 walls for 3Rx2R)
            wallSegments.push({ position: new THREE.Vector3(0, WALL_HEIGHT / 2, -R), rotation: 0, length: 3*R, isOuterBoundary: true, direction: 'n' }); // N
            wallSegments.push({ position: new THREE.Vector3(R+H, WALL_HEIGHT / 2, 0), rotation: Math.PI / 2, length: 2*R, isOuterBoundary: true, direction: 'e' }); // E
            wallSegments.push({ position: new THREE.Vector3(0, WALL_HEIGHT / 2, R), rotation: Math.PI, length: 3*R, isOuterBoundary: true, direction: 's' }); // S
            wallSegments.push({ position: new THREE.Vector3(-(R+H), WALL_HEIGHT / 2, 0), rotation: -Math.PI / 2, length: 2*R, isOuterBoundary: true, direction: 'w' }); // W
            // Floor (Single 3Rx2R piece)
            floorSegments.push({ position: new THREE.Vector3(0, 0, 0), width: 3*R, depth: 2*R });
            break;

        case 'U_SHAPE_DOWN': // U-shaped room with opening at the bottom
            // Define the room as a U shape with the opening at the bottom (south)
            // The room consists of a top horizontal bar (3x1) and two vertical legs (1x1 each)
            wallSegments = [
                // Outer perimeter walls
                // North wall (top of the U)
                { position: new THREE.Vector3(0, WALL_HEIGHT / 2, -R), rotation: 0, length: 3*R, isOuterBoundary: true, direction: 'n' },

                // East walls
                // Top-right corner to middle-right
                { position: new THREE.Vector3(R+H, WALL_HEIGHT / 2, -H), rotation: Math.PI / 2, length: R, isOuterBoundary: true, direction: 'e' },
                // Middle-right to bottom-right
                { position: new THREE.Vector3(R+H, WALL_HEIGHT / 2, H), rotation: Math.PI / 2, length: R, isOuterBoundary: true, direction: 'e' },

                // South walls (bottom of the legs)
                // Bottom of right leg
                { position: new THREE.Vector3(R, WALL_HEIGHT / 2, R), rotation: Math.PI, length: R, isOuterBoundary: true, direction: 's' },
                // Bottom of left leg
                { position: new THREE.Vector3(-R, WALL_HEIGHT / 2, R), rotation: Math.PI, length: R, isOuterBoundary: true, direction: 's' },

                // West walls
                // Bottom-left to middle-left
                { position: new THREE.Vector3(-(R+H), WALL_HEIGHT / 2, H), rotation: -Math.PI / 2, length: R, isOuterBoundary: true, direction: 'w' },
                // Middle-left to top-left
                { position: new THREE.Vector3(-(R+H), WALL_HEIGHT / 2, -H), rotation: -Math.PI / 2, length: R, isOuterBoundary: true, direction: 'w' },

                // Inner walls (around the opening)
                // Inner east wall (left side of right leg)
                { position: new THREE.Vector3(H, WALL_HEIGHT / 2, H), rotation: Math.PI / 2, length: R, isOuterBoundary: true, direction: 'e' },
                // Inner west wall (right side of left leg)
                { position: new THREE.Vector3(-H, WALL_HEIGHT / 2, H), rotation: -Math.PI / 2, length: R, isOuterBoundary: true, direction: 'w' },
                // Top wall connecting the legs (invisible collision wall)
                { position: new THREE.Vector3(0, WALL_HEIGHT / 2, 0), rotation: 0, length: R, isOuterBoundary: true, direction: 'n', isVisible: false }
            ];

            // Floor segments
            // Top horizontal bar (3x1)
            floorSegments.push({ position: new THREE.Vector3(0, 0, -H), width: 3*R, depth: R });
            // Bottom-left leg (1x1)
            floorSegments.push({ position: new THREE.Vector3(-R, 0, H), width: R, depth: R });
            // Bottom-right leg (1x1)
            floorSegments.push({ position: new THREE.Vector3(R, 0, H), width: R, depth: R });
            break;

        case 'SQUARE_1X1':
        default:
            // Walls
            wallSegments = [
                { position: new THREE.Vector3(0, WALL_HEIGHT / 2, -halfDepth), rotation: 0, length: roomWidth, isOuterBoundary: true, direction: 'n' }, // N
                { position: new THREE.Vector3(halfWidth, WALL_HEIGHT / 2, 0), rotation: Math.PI / 2, length: roomDepth, isOuterBoundary: true, direction: 'e' }, // E
                { position: new THREE.Vector3(0, WALL_HEIGHT / 2, halfDepth), rotation: Math.PI, length: roomWidth, isOuterBoundary: true, direction: 's' }, // S
                { position: new THREE.Vector3(-halfWidth, WALL_HEIGHT / 2, 0), rotation: -Math.PI / 2, length: roomDepth, isOuterBoundary: true, direction: 'w' } // W
            ];
            // Floor (Single piece)
            floorSegments.push({ position: new THREE.Vector3(0, 0, 0), width: roomWidth, depth: roomDepth });
            break;
    }
    return { wallSegments, floorSegments };
}

// Helper function has been moved to the top of the file

/**
 * Check if a position is within the valid area of a room shape
 * @param {number} x - X coordinate
 * @param {number} z - Z coordinate
 * @param {string} roomShape - Room shape key
 * @returns {boolean} True if position is valid for the room shape
 */
function _isPositionInRoomShape(x, z, roomShape) {
    if (!roomShape) {
        return true; // If no shape specified, allow all positions
    }

    // Room constants (matching DungeonHandler.js)
    const R = 14; // ROOM_WORLD_SIZE
    const H = 7;  // Half of ROOM_WORLD_SIZE

    switch (roomShape) {
        case 'SQUARE_1X1':
        case 'RECTANGULAR':
        case 'RECT_2X1':
        case 'RECT_1X2':
        case 'SQUARE_2X2':
        case 'RECT_3X1':
        case 'RECT_1X3':
        case 'RECT_3X2':
            // Simple rectangular shapes - use bounding box validation
            return true; // These are already handled by bounding box

        case 'L_SHAPE':
            // CORRECTED L-shape: vertical part (top-left) and horizontal part (bottom)
            // Based on floor segments: (-H,0,-H) with R×R and (0,0,H) with 2R×R
            // Valid areas:
            // - Vertical part (top-left): x from -R to 0, z from -R to 0
            // - Horizontal part (bottom): x from -R to R, z from 0 to R
            const inVerticalPart = (x >= -R && x <= 0 && z >= -R && z <= 0);
            const inHorizontalPart = (x >= -R && x <= R && z >= 0 && z <= R);
            return inVerticalPart || inHorizontalPart;

        case 'T_SHAPE':
            // T-shape: horizontal bar at top (3x1) and vertical stem at bottom (1x1)
            // Top bar: (-R-H to R+H, -R to 0)
            // Bottom stem: (-H to H, 0 to R)
            const inTopBar = (x >= -(R + H) && x <= (R + H) && z >= -R && z <= 0);
            const inBottomStem = (x >= -H && x <= H && z >= 0 && z <= R);
            return inTopBar || inBottomStem;

        case 'U_SHAPE_DOWN':
            // U-shape with opening at bottom: top bar (3x1) and two side legs (1x1 each)
            // Top bar: (-R-H to R+H, -R to 0)
            // Left leg: (-R-H to -H, 0 to R)
            // Right leg: (H to R+H, 0 to R)
            const inUTopBar = (x >= -(R + H) && x <= (R + H) && z >= -R && z <= 0);
            const inULeftLeg = (x >= -(R + H) && x <= -H && z >= 0 && z <= R);
            const inURightLeg = (x >= H && x <= (R + H) && z >= 0 && z <= R);
            return inUTopBar || inULeftLeg || inURightLeg;

        case 'CROSS_SHAPE':
            // Cross shape: horizontal bar (3x1) and vertical bar (1x3) intersecting at center
            // Horizontal: (-R-H to R+H, -H to H)
            // Vertical: (-H to H, -R-H to R+H)
            const inHorizontalBar = (x >= -(R + H) && x <= (R + H) && z >= -H && z <= H);
            const inVerticalBar = (x >= -H && x <= H && z >= -(R + H) && z <= (R + H));
            return inHorizontalBar || inVerticalBar;

        case 'BOSS_ARENA':
            // Boss arena is typically much larger - use bounding box validation
            return true;

        default:
            console.warn(`[RoomShapeValidation] Unknown room shape: ${roomShape}, allowing position`);
            return true;
    }
}

// Helper function for door proximity check
function isTooCloseToDoors(position, doorPositions, minDistance) {
    for (const dir in doorPositions) {
        const doorPos = doorPositions[dir];
        if (doorPos && position.distanceTo(doorPos) < minDistance) {
            return true; // Too close to this door
        }
    }
    return false; // Far enough from all doors
}

// Helper function for collision check with already placed objects
function collidesWithPlacedObjects(position, radius, placedObjects) {
    for (const placed of placedObjects) {
        const distance = position.distanceTo(placed.position);
        if (distance < radius + placed.radius) {
            return true; // Collision detected
        }
    }
    return false; // No collision
}

// --- NEW: Helper function to place interior objects ---
function _placeInteriorObjects(roomGroup, areaData, roomData, collisionMeshes, lights, wallSegments, floorSegments, doorPositions, isDarkRoom) {
    // --- NEW: Prevent spawning in room 0 ---
    if (roomData.id === 0) {
        console.log(`[PlaceObjects][${roomData.id}] Skipping interior object placement for room 0.`);
        return; // Do not place any objects in the starting room
    }
    // --- END Prevent spawning in room 0 ---

    // --- NEW LOGGING: Inspect incoming areaData ---
    console.log(`[PlaceObjects][${roomData.id}] Received areaData.interiorObjects:`, JSON.stringify(areaData.interiorObjects, null, 2));
    // --- END LOGGING ---

    const random = mulberry32(roomData.id * 97 + 53);
    const objectsToPlace = [];
    const placedObjectData = []; // --- NEW: Track placed objects ---

    // --- NEW: Door Torch Logic ---
    const placeDoorTorches = random() < 0.30; // 30% chance per room
    const torchPrefabFunc = getPrefabFunction('torch', 'interior'); // Get the torch function once

    if (placeDoorTorches && torchPrefabFunc) {
        console.log(`[PlaceObjects][${roomData.id}] Placing torches next to doors (30% chance triggered).`);
        Object.entries(doorPositions).forEach(([dir, doorPos]) => {
            if (!doorPos) return; // Skip if no door in this direction

            // Determine wall rotation and tangent based on door direction
            let wallRotation = 0;
            const tangent = new THREE.Vector3();
            const inwardOffsetMult = 0.03; // Small inward offset
            const inwardNormal = new THREE.Vector3(); // Normal pointing into the room
            const outwardNormal = new THREE.Vector3(); // Normal pointing out of the wall

            switch(dir) {
                case 'n':
                    wallRotation = 0;
                    tangent.set(1, 0, 0); // Tangent is along X-axis
                    inwardNormal.set(0, 0, 1); // Inward is +Z
                    outwardNormal.set(0, 0, -1); // Outward is -Z
                    break;
                case 'e':
                    wallRotation = Math.PI / 2;
                    tangent.set(0, 0, -1); // Tangent is along -Z-axis
                    inwardNormal.set(-1, 0, 0); // Inward is -X
                    outwardNormal.set(1, 0, 0); // Outward is +X
                    break;
                case 's':
                    wallRotation = Math.PI;
                    tangent.set(-1, 0, 0); // Tangent is along -X-axis
                    inwardNormal.set(0, 0, -1); // Inward is -Z
                    outwardNormal.set(0, 0, 1); // Outward is +Z
                    break;
                case 'w':
                    wallRotation = -Math.PI / 2;
                    tangent.set(0, 0, 1); // Tangent is along Z-axis
                    inwardNormal.set(1, 0, 0); // Inward is +X
                    outwardNormal.set(-1, 0, 0); // Outward is -X
                    break;
            }

            const sideOffset = DOOR_WIDTH / 2 + VOXEL_SIZE * 2.5; // Offset from door center along the wall

            // Calculate positions for Left and Right torches
            const leftPos = doorPos.clone().addScaledVector(tangent, -sideOffset);
            const rightPos = doorPos.clone().addScaledVector(tangent, sideOffset);

            [leftPos, rightPos].forEach((torchBasePos, idx) => {
                const torchResult = torchPrefabFunc({ seed: random() * 1000 });
                // Handle both return types: object with group property or direct group
                const torchGroup = torchResult && torchResult.group ? torchResult.group : torchResult;

                if (torchGroup instanceof THREE.Group) {
                    // Apply outward normal offset first to place it on the wall surface
                    torchGroup.position.copy(torchBasePos).addScaledVector(outwardNormal, WALL_DEPTH / 2);
                    // Then apply small inward offset for visibility
                    torchGroup.position.addScaledVector(inwardNormal, inwardOffsetMult);

                    // Set rotation to face into the room (based on inward normal)
                    torchGroup.lookAt(torchGroup.position.clone().add(inwardNormal));

                    // Adjust height
                    torchGroup.position.y = WALL_HEIGHT * 0.6;
                    roomGroup.add(torchGroup);

                    // Add light source - Fixed to properly use the flame position
                    if (torchResult.flameLocalPosition) {
                        const light = _createTorchLight();
                        // Convert flame's local position to world position
                        const flameWorldPos = torchGroup.localToWorld(torchResult.flameLocalPosition.clone());
                        light.position.copy(flameWorldPos);
                        lights.push(light);
                        console.log(`[PlaceObjects][${roomData.id}] Added light source for door torch (${idx === 0 ? 'Left' : 'Right'}) next to ${dir.toUpperCase()} door.`);
                    }
                    console.log(`[PlaceObjects][${roomData.id}] Placed door torch (${idx === 0 ? 'Left' : 'Right'}) next to ${dir.toUpperCase()} door.`);
                }
            });
        });
    } else if (placeDoorTorches && !torchPrefabFunc) {
        console.warn(`[PlaceObjects][${roomData.id}] Wanted to place door torches, but torch prefab function was not found.`);
    }
    // --- END Door Torch Logic ---

    // If no interior objects are defined in areaData, exit early (after potentially placing door torches)
    if (!areaData.interiorObjects || areaData.interiorObjects.length === 0) {
        return;
    }

    // 1. Determine which objects to spawn and how many (from areaData)
    areaData.interiorObjects.forEach(objDef => {
        if (random() < objDef.probability) {

            let minQty = objDef.minQuantity;
            let maxQty = objDef.maxQuantity;

            // --- Dynamic Quantity Scaling for Torches ---
            if (objDef.type === 'torch') {
                const roomArea = (roomData.bounds && roomData.bounds.width && roomData.bounds.depth)
                                 ? roomData.bounds.width * roomData.bounds.depth
                                 : 196; // Default to small area if bounds are missing

                // Store original quantities from configuration
                const configMinQty = minQty;
                const configMaxQty = maxQty;

                // Define thresholds and scaled quantities
                const SMALL_AREA_THRESHOLD = 14 * 14; // 196
                const MEDIUM_AREA_THRESHOLD = 28 * 14; // 392

                // Scale quantities based on room size while respecting minimums
                // Reduced quantities for more realistic torch placement
                if (roomArea <= SMALL_AREA_THRESHOLD) {
                    minQty = 1; // Reduced to absolute minimum
                    maxQty = 2; // Maximum 2 for small rooms
                } else if (roomArea <= MEDIUM_AREA_THRESHOLD) {
                    minQty = 2; // Two torches minimum for medium rooms
                    maxQty = 3; // Maximum 3 for medium rooms
                } else { // Large rooms
                    minQty = 2; // Two torches minimum for large rooms
                    maxQty = 4; // Maximum 4 for large rooms
                }

                // Apply dark room logic: ensure at least 1 torch if NOT dark
                if (!isDarkRoom) {
                    minQty = Math.max(1, minQty);
                }
                console.log(`[PlaceObjects][${roomData.id}] Scaled TORCH quantity for area ${roomArea.toFixed(0)} (Dark: ${isDarkRoom}): min=${minQty}, max=${maxQty}`);
            }
            // --- End Dynamic Quantity Scaling ---

            // Use the potentially scaled quantities
            const quantity = Math.floor(random() * (maxQty - minQty + 1)) + minQty;

            // Logging for calculated quantity (keep existing)
            if (objDef.type === 'torch') {
                console.log(`[PlaceObjects][${roomData.id}] Calculated final quantity for TORCH: ${quantity} (using scaled min/max)`);
            }

            for (let i = 0; i < quantity; i++) {
                objectsToPlace.push({
                    type: objDef.type,
                    prefabFunc: getPrefabFunction(objDef.type, 'interior'),
                    placement: objDef.placement || 'floor',
                    seed: random() * 10000 + i,
                    isDestructible: objDef.isDestructible || false,
                    destructionEffect: objDef.destructionEffect || null,
                    placementDetail: objDef.placementDetail || ['random'],
                });
            }
        }
    });

    // Filter out any torches added by the area definition if door torches were already placed
    // (to avoid double counting or exceeding limits)
    const finalObjectsToPlace = objectsToPlace;

    if (finalObjectsToPlace.length === 0) return; // Exit if no objects left after filtering

    console.log(`[PlaceObjects] Attempting to place ${finalObjectsToPlace.length} REGULAR objects for room ${roomData.id}`);
    console.log(`[PlaceObjects][${roomData.id}] Regular objects to be placed:`, finalObjectsToPlace.map(o => o.type));

    // 2. Get potential placement locations
    // Create a separate array for each wall direction
    const wallPointsByDirection = {
        n: [], e: [], s: [], w: []
    };
    console.log(`[PlaceObjects][${roomData.id}] Processing ${wallSegments.length} wall segments for points...`);
    wallSegments.forEach((wallSeg, segIndex) => {
        // Check if this segment corresponds to a door location
        const isDoorWall = Object.entries(doorPositions).some(([dir, pos]) =>
            pos &&
            Math.abs(pos.x - wallSeg.position.x) < wallSeg.length / 2 + DOOR_WIDTH / 2 + 0.1 && // Check overlap with door width
            Math.abs(pos.z - wallSeg.position.z) < WALL_DEPTH / 2 + 0.1 &&
            wallSeg.direction === dir
        );

        // Only skip if it's a door wall - remove isVisible check to allow more placement points
        if (!isDoorWall && wallSeg.isOuterBoundary) {
            // NEW: Skip if wall is not visible
            if (!wallSeg.isVisible) {
                console.log(`[Wall ${wallSeg.direction}] Skipping points because wall is not visible.`);
                return;
            }

            // Default point spacing for most objects
            const pointSpacing = VOXEL_SIZE * 3;
            const numPoints = Math.max(2, Math.floor(wallSeg.length / pointSpacing));
            const wallTangent = new THREE.Vector3(Math.cos(wallSeg.rotation), 0, -Math.sin(wallSeg.rotation));
            const wallStart = wallSeg.position.clone().addScaledVector(wallTangent, -wallSeg.length / 2);

            // Calculate wall normal based on direction instead of rotation
            let wallNormal;
            switch(wallSeg.direction) {
                case 'n': wallNormal = new THREE.Vector3(0, 0, -1); break; // North wall - normal points north (-Z)
                case 's': wallNormal = new THREE.Vector3(0, 0, 1); break;  // South wall - normal points south (+Z)
                case 'e': wallNormal = new THREE.Vector3(1, 0, 0); break;  // East wall - normal points east (+X)
                case 'w': wallNormal = new THREE.Vector3(-1, 0, 0); break; // West wall - normal points west (-X)
                default:
                    // Fallback to rotation-based calculation if no direction
                    wallNormal = new THREE.Vector3(Math.sin(wallSeg.rotation), 0, Math.cos(wallSeg.rotation));
            }
            const inwardNormal = wallNormal.clone().negate(); // Normal pointing into the room

            console.log(`[Wall ${wallSeg.direction}] Adding ${numPoints} points at ${wallSeg.position.x.toFixed(1)},${wallSeg.position.z.toFixed(1)}`);

            for(let i = 0; i < numPoints; i++) {
                const t = (i + 0.5) / numPoints;
                const centerLinePoint = wallStart.clone().addScaledVector(wallTangent, wallSeg.length * t);
                const placementPoint = centerLinePoint.clone().addScaledVector(wallNormal, -WALL_DEPTH / 2); // Move to inner surface

                // ADDITIONAL SAFETY: Validate wall point is within room shape
                if (!_isPositionInRoomShape(placementPoint.x, placementPoint.z, shapeKey)) {
                    continue; // Skip wall points outside valid room shape
                }

                const pointData = {
                    position: placementPoint,
                    rotationY: wallSeg.rotation,
                    wallNormal: wallNormal, // Outward normal
                    inwardNormal: inwardNormal, // Inward normal
                    direction: wallSeg.direction
                };

                if (wallSeg.direction && wallPointsByDirection[wallSeg.direction]) {
                    wallPointsByDirection[wallSeg.direction].push(pointData);
                }
            }
        } else {
            if (isDoorWall) console.log(`[Wall ${wallSeg.direction}] Skipping points because it contains a door.`);
        }
    });

    // Calculate total points available
    let totalWallPoints = 0;
    Object.values(wallPointsByDirection).forEach(arr => totalWallPoints += arr.length);
    console.log(`[PlaceObjects] REGULAR Wall Points - N:${wallPointsByDirection.n.length}, E:${wallPointsByDirection.e.length}, S:${wallPointsByDirection.s.length}, W:${wallPointsByDirection.w.length} (Total: ${totalWallPoints})`);
    const allWallPoints = [].concat(...Object.values(wallPointsByDirection)); // Combine wall points *after* counting

    // --- NEW: Calculate Floor Points (Grid-based approach) ---
    const allFloorPoints = [];
    // Need room dimensions for bounding box calculation - retrieve from roomData if available, or use shapeKey
    const { shapeKey } = roomData;
    const defaultSize = ROOM_WORLD_SIZE;
    let roomWidth = roomData.bounds?.width || defaultSize; // Use pre-calculated if available
    let roomDepth = roomData.bounds?.depth || defaultSize;
    // If bounds weren't passed/calculated earlier, estimate based on shapeKey
    if (!roomData.bounds?.width || !roomData.bounds?.depth) {
        // Estimate dimensions based on shapeKey - this should match the logic in generateRoomVisuals
        switch (shapeKey) {
           case 'RECTANGULAR': case 'RECT_2X1': roomWidth = defaultSize * 2; roomDepth = defaultSize; break;
           case 'L_SHAPE': roomWidth = defaultSize * 2; roomDepth = defaultSize * 2; break;
           case 'T_SHAPE': roomWidth = defaultSize * 3; roomDepth = defaultSize * 2; break;
           case 'CROSS_SHAPE': roomWidth = defaultSize * 3; roomDepth = defaultSize * 3; break;
           case 'SQUARE_2X2': roomWidth = defaultSize * 2; roomDepth = defaultSize * 2; break;
           case 'RECT_3X1': roomWidth = defaultSize * 3; roomDepth = defaultSize; break;
           case 'RECT_1X2': roomWidth = defaultSize; roomDepth = defaultSize * 2; break;
           case 'RECT_1X3': roomWidth = defaultSize; roomDepth = defaultSize * 3; break;
           case 'RECT_3X2': roomWidth = defaultSize * 3; roomDepth = defaultSize * 2; break;
           case 'U_SHAPE_DOWN': roomWidth = defaultSize * 3; roomDepth = defaultSize * 2; break;
           case 'SQUARE_1X1': default: roomWidth = defaultSize; roomDepth = defaultSize; break;
       }
    }
    const roomBounds = _calculateRoomBoundingBox(shapeKey, roomWidth, roomDepth);
    const gridSpacing = VOXEL_SIZE * 3; // Spacing between potential points
    const borderMargin = VOXEL_SIZE * 3; // Margin from the edges

    // Generate grid points within the bounding box
    for (let x = roomBounds.minX + borderMargin; x < roomBounds.maxX - borderMargin; x += gridSpacing) {
        for (let z = roomBounds.minZ + borderMargin; z < roomBounds.maxZ - borderMargin; z += gridSpacing) {
            const pointPos = new THREE.Vector3(x, 0, z);

            // CRITICAL FIX: Check room shape validation first
            if (!_isPositionInRoomShape(x, z, shapeKey)) {
                continue; // Skip positions outside the valid room shape
            }

            // Quick check: Ensure point is inside at least one floor segment
            const isInsideFloor = floorSegments.some(seg => {
                const minX = seg.position.x - seg.width / 2;
                const maxX = seg.position.x + seg.width / 2;
                const minZ = seg.position.z - seg.depth / 2;
                const maxZ = seg.position.z + seg.depth / 2;
                return pointPos.x >= minX && pointPos.x <= maxX && pointPos.z >= minZ && pointPos.z <= maxZ;
            });

            if (isInsideFloor) {
                allFloorPoints.push({ position: pointPos });
            }
        }
    }
    console.log(`[PlaceObjects][${roomData.id}] Generated ${allFloorPoints.length} potential floor placement points.`);
    // --- END Floor Points Calculation ---

    // --- DEBUG LOGGING: Point Counts ---
    console.log(`[PlaceObjects][${roomData.id}] Calculated placement points: WALL=${allWallPoints.length}, FLOOR=${allFloorPoints.length}`);
    // --- END DEBUG ---

    // Shuffle the lists initially (Make sure shuffleArray is defined/accessible)
    function shuffleArray(array, rng) {
        for (let i = array.length - 1; i > 0; i--) {
            const j = Math.floor(rng() * (i + 1));
            [array[i], array[j]] = [array[j], array[i]];
        }
    }
    shuffleArray(allWallPoints, random); // Use 'random' which is the seeded RNG
    shuffleArray(allFloorPoints, random); // Use 'random' which is the seeded RNG

    // --- DEBUG LOGGING: Object Counts ---
    const countsByType = {};
    finalObjectsToPlace.forEach(obj => {
        countsByType[obj.type] = (countsByType[obj.type] || 0) + 1;
    });
    console.log(`[PlaceObjects][${roomData.id}] Objects to attempt placement:`, JSON.stringify(countsByType));
    // --- END DEBUG ---

    // 3. Place the collected objects
    console.log(`[PlaceObjects][${roomData.id}] Placing ${finalObjectsToPlace.length} total interior objects.`);

    // --- Separate Pillars from other objects ---
    const pillarObjects = finalObjectsToPlace.filter(o => o.type === 'stone_pillar');
    const otherObjects = finalObjectsToPlace.filter(o => o.type !== 'stone_pillar');
    // -----------------------------------------

    let placedCount = 0; // Track successfully placed objects

    // --- NEW: Place Pillars First (Guaranteed Corners if possible) ---
    if (pillarObjects.length > 0) {
        console.log(`[PlaceObjects][${roomData.id}] Attempting to place ${pillarObjects.length} Pillars in corners.`);
        const cornerSize = Math.min(roomBounds.width, roomBounds.depth) * 0.25;
        const objectRadius = getObjectRadius('stone_pillar');

        const corners = [
            { x: roomBounds.minX + cornerSize / 2, z: roomBounds.minZ + cornerSize / 2, name: 'SW' },
            { x: roomBounds.maxX - cornerSize / 2, z: roomBounds.minZ + cornerSize / 2, name: 'SE' },
            { x: roomBounds.minX + cornerSize / 2, z: roomBounds.maxZ - cornerSize / 2, name: 'NW' },
            { x: roomBounds.maxX - cornerSize / 2, z: roomBounds.maxZ - cornerSize / 2, name: 'NE' },
        ].filter(corner => {
            // CRITICAL FIX: Filter out corners that are outside the valid room shape
            const isValidCorner = _isPositionInRoomShape(corner.x, corner.z, shapeKey);
            if (!isValidCorner) {
                console.log(`[PlaceObjects][${roomData.id}] Filtering out corner ${corner.name} - outside valid room shape for ${shapeKey}`);
            }
            return isValidCorner;
        });

        let pillarsPlaced = 0;
        const availableFloorPointsForPillars = [...allFloorPoints]; // Use a copy
        shuffleArray(availableFloorPointsForPillars, random);

        for (const corner of corners) {
            if (pillarsPlaced >= pillarObjects.length) break; // Stop if we've placed the requested number

            let chosenLocation = null;
            let placedInThisCorner = false;
            let attempts = 0;
            const targetCornerPos = new THREE.Vector3(corner.x, 0, corner.z);

            // --- Find nearest available point to the target corner position ---
            let bestPoint = null;
            let minDistSq = Infinity;

            // Search through ALL available floor points for the closest valid one
            for (const pointData of availableFloorPointsForPillars) {
                const candidatePosition = pointData.position;
                const distSq = candidatePosition.distanceToSquared(targetCornerPos);

                // Check validity (collision, doors) and if it's closer than the current best
                const farEnoughFromDoors = !isTooCloseToDoors(candidatePosition, doorPositions, MIN_DISTANCE_FROM_DOOR + objectRadius);
                const noCollision = !collidesWithPlacedObjects(candidatePosition, objectRadius, placedObjectData);

                if (farEnoughFromDoors && noCollision && distSq < minDistSq) {
                    minDistSq = distSq;
                    bestPoint = pointData;
                }
            }
            // --- End Search ---

            // If we found a suitable point near the corner
            if (bestPoint) {
                 chosenLocation = bestPoint;
                 placedInThisCorner = true; // We placed the pillar associated with this corner
            } else {
                 console.log(`[PlaceObjects][${roomData.id}] No valid point found near corner ${corner.name}.`);
                 continue; // Skip to the next corner if no valid point found nearby
            }

            // --- Try finding a point *within* this corner region first (REMOVED - Using nearest point instead)
            /*
            const isCorner = (point) => { ... };
            const cornerPoints = availableFloorPointsForPillars.filter(isCorner);
            shuffleArray(cornerPoints, random);

            for (const candidateLocation of cornerPoints) {
                 attempts++;
                 const candidatePosition = candidateLocation.position;
                 const farEnoughFromDoors = !isTooCloseToDoors(candidatePosition, doorPositions, MIN_DISTANCE_FROM_DOOR + objectRadius);
                 const noCollision = !collidesWithPlacedObjects(candidatePosition, objectRadius, placedObjectData);

                 if (farEnoughFromDoors && noCollision) {
                     chosenLocation = candidateLocation;
                     placedInThisCorner = true;
                     break;
                 }
                 if (attempts >= MAX_PLACEMENT_ATTEMPTS / 2) break; // Limit attempts within corner region
            }
            */

            // If a valid spot was found (now using nearest point logic)
            if (chosenLocation && placedInThisCorner) {
                const objToPlace = pillarObjects[pillarsPlaced]; // Get the next pillar object data

                // --- Remove chosen location from ORIGINAL list ---
                const originalList = allFloorPoints; // Modify the main list
                const originalIndex = originalList.findIndex(p => p === chosenLocation);
                if (originalIndex !== -1) { originalList.splice(originalIndex, 1); }
                else {
                    const fallbackIndex = originalList.findIndex(p => p.position.equals(chosenLocation.position));
                    if (fallbackIndex !== -1) { originalList.splice(fallbackIndex, 1); }
                    else { console.warn(`[PillarPlace] Chosen corner location couldn't be removed.`); }
                }
                // Also remove from the temporary list used for this corner search
                 const tempIndex = availableFloorPointsForPillars.findIndex(p => p === chosenLocation);
                 if (tempIndex !== -1) availableFloorPointsForPillars.splice(tempIndex, 1);
                // ---------------------------------------------

                // --- Instantiate and Place Pillar ---
                let prefabOptions = { seed: objToPlace.seed, wallHeight: WALL_HEIGHT };
                const objectResult = objToPlace.prefabFunc(prefabOptions);
                let objectGroup = objectResult.group || objectResult;

                if (objectGroup && objectGroup instanceof THREE.Object3D) {
                    objectGroup.position.copy(chosenLocation.position);
                    objectGroup.rotation.y = random() * Math.PI * 2;
                    objectGroup.position.y = 0; // Prefab should handle its internal offset
                    roomGroup.add(objectGroup);

                    // UserData and Collision Mesh
                    if (!objectGroup.userData) objectGroup.userData = {};
                    objectGroup.userData.isDestructible = objToPlace.isDestructible;
                    objectGroup.userData.destructionEffect = objToPlace.destructionEffect;
                    objectGroup.userData.objectType = objToPlace.type;
                    collisionMeshes.push(objectGroup); // Add group itself as collider

                    // Track placement
                    placedObjectData.push({ position: objectGroup.position.clone(), radius: objectRadius });
                    placedCount++;
                    pillarsPlaced++;
                    console.log(`[PlaceObjects][${roomData.id}] Successfully placed PILLAR in corner ${corner.name}.`);
                } else {
                     console.error(`[PlaceObjects][${roomData.id}] Pillar prefab function failed.`);
                }
                // ------------------------------------
                chosenLocation = null; // Reset for next corner
            } else {
                console.log(`[PlaceObjects][${roomData.id}] Could not place pillar in corner ${corner.name}. No valid spot found.`);
            }
        }
        console.log(`[PlaceObjects][${roomData.id}] Finished placing ${pillarsPlaced} / ${pillarObjects.length} pillars.`);
    }
    // --- END Place Pillars ---

    // --- Place Other Objects (using the remaining points) ---
    console.log(`[PlaceObjects][${roomData.id}] Placing ${otherObjects.length} other interior objects.`);
    shuffleArray(otherObjects, random); // Shuffle remaining objects

    for (const objToPlace of otherObjects) { // Loop through NON-PILLAR objects
        const placementType = objToPlace.placement; // 'wall' or 'floor'
        // Use the potentially modified original point lists (pillars removed points)
        const availablePointsList = placementType === 'wall' ? allWallPoints : allFloorPoints;

        // --- Filter locations based on placementDetail (Corners, Random, etc.) ---
        let potentialLocations = availablePointsList; // Start with all available for the type
        const details = objToPlace.placementDetail;
        const objectRadius = getObjectRadius(objToPlace.type);
        if (placementType === 'floor' && details && !(details.length === 1 && details[0] === 'random')) {
            const cornerSize = Math.min(roomBounds.width, roomBounds.depth) * 0.25;
            const isCorner = (point) => {
                const p = point.position;
                const isNearMinX = p.x < roomBounds.minX + cornerSize;
                const isNearMaxX = p.x > roomBounds.maxX - cornerSize;
                const isNearMinZ = p.z < roomBounds.minZ + cornerSize;
                const isNearMaxZ = p.z > roomBounds.maxZ - cornerSize;
                const inCornerArea = (isNearMinX && isNearMinZ) || (isNearMinX && isNearMaxZ) ||
                                   (isNearMaxX && isNearMinZ) || (isNearMaxX && isNearMaxZ);

                // CRITICAL FIX: Also validate that the corner position is within the valid room shape
                return inCornerArea && _isPositionInRoomShape(p.x, p.z, shapeKey);
            };

            let allowedPoints = [];
            if (details.includes('random')) allowedPoints = [...availablePointsList];
            if (details.includes('corners')) allowedPoints = allowedPoints.concat(availablePointsList.filter(isCorner));
            potentialLocations = [...new Set(allowedPoints)];
            console.log(`[PlaceObjects][${roomData.id}] Filtered points for '${objToPlace.type}' [${details.join(', ')}]: ${potentialLocations.length} points.`);
        } else {
            console.log(`[PlaceObjects][${roomData.id}] Using all ${availablePointsList.length} ${placementType} points for '${objToPlace.type}' (random or no detail).`);
        }

        if (potentialLocations.length === 0) {
            console.warn(`[PlaceObjects][${roomData.id}] No potential placement points left for object type '${objToPlace.type}' after detail filtering. Skipping.`);
            continue;
        }

        let chosenLocation = null;
        let attempts = 0;
        shuffleArray(potentialLocations, random);

        for (const candidateLocation of potentialLocations) {
            attempts++;
            const candidatePosition = candidateLocation.position;
            let farEnoughFromDoors = true;
            // Check door proximity for both floor AND wall objects
            farEnoughFromDoors = !isTooCloseToDoors(candidatePosition, doorPositions, MIN_DISTANCE_FROM_DOOR + objectRadius);
            const noCollision = !collidesWithPlacedObjects(candidatePosition, objectRadius, placedObjectData);

            if (farEnoughFromDoors && noCollision) {
                chosenLocation = candidateLocation;
                // console.log(`[PlaceObjects][${roomData.id}] Found valid spot for '${objToPlace.type}' after ${attempts} checks.`);
                break;
            }
            if (attempts >= MAX_PLACEMENT_ATTEMPTS) {
                // console.log(`[PlaceObjects][${roomData.id}] Reached max placement attempts (${MAX_PLACEMENT_ATTEMPTS}) for '${objToPlace.type}'.`);
                break;
            }
        }

        if (!chosenLocation) {
            console.warn(`[PlaceObjects][${roomData.id}] Could not find a valid placement location for object type '${objToPlace.type}'. Skipping instance.`);
            continue;
        }

        const originalList = placementType === 'wall' ? allWallPoints : allFloorPoints;
        const originalIndex = originalList.findIndex(p => p === chosenLocation);
        if (originalIndex !== -1) {
            originalList.splice(originalIndex, 1);
        } else {
            const fallbackIndex = originalList.findIndex(p => p.position.equals(chosenLocation.position));
            if (fallbackIndex !== -1) {
                originalList.splice(fallbackIndex, 1);
            } else {
                console.warn(`[PlaceObjects][${roomData.id}] OTHER Object: Chosen location couldn't be removed.`);
                 const potentialIndex = potentialLocations.findIndex(p => p === chosenLocation);
                 if(potentialIndex !== -1) potentialLocations.splice(potentialIndex, 1);
            }
        }

        let prefabOptions = { seed: objToPlace.seed };
        // No special options needed for most other objects currently
        const objectResult = objToPlace.prefabFunc(prefabOptions);

        // Handle both return types: object with group property or direct group
        let objectGroup = objectResult && objectResult.group ? objectResult.group : objectResult;

        if (!objectGroup || !(objectGroup instanceof THREE.Object3D)) {
            console.error(`[PlaceObjects][${roomData.id}] Prefab function failed for '${objToPlace.type}'.`);
            continue;
        }

        if (!objectGroup.userData) objectGroup.userData = {};
        objectGroup.userData.isDestructible = objToPlace.isDestructible;
        objectGroup.userData.destructionEffect = objToPlace.destructionEffect;
        objectGroup.userData.objectType = objToPlace.type;

        if (placementType === 'wall') {
            if (!(objectGroup instanceof THREE.Group)) {
                // Wrap if needed...
            }
            // ENHANCED Wall placement logic with proper offsets
             objectGroup.position.copy(chosenLocation.position);
             objectGroup.lookAt(objectGroup.position.clone().add(chosenLocation.inwardNormal));

             // CRITICAL FIX: Object-specific inward offsets for proper wall alignment
             let inwardOffset = 0.4; // INCREASED: Much larger base offset to close gaps
             if (objToPlace.type === 'vine') {
                 inwardOffset = 0.3; // INCREASED: Vines closer to wall but still substantial offset
             } else if (objToPlace.type === 'torch') {
                 inwardOffset = 0.5; // INCREASED: Torches need significant offset to touch wall
             } else if (objToPlace.type === 'aether_torch') {
                 inwardOffset = 0.5; // INCREASED: Aether torches same as regular torches
             }

             objectGroup.position.addScaledVector(chosenLocation.inwardNormal, inwardOffset);
             let yPos = WALL_HEIGHT * 0.5;
             if (objToPlace.type === 'vine') yPos = WALL_HEIGHT * 0.85;
             else if (objToPlace.type === 'torch') yPos = WALL_HEIGHT * 0.6;
             objectGroup.position.y = yPos;
        } else { // Floor placement
            objectGroup.position.copy(chosenLocation.position);
            objectGroup.rotation.y = random() * Math.PI * 2;

            // CRITICAL FIX: Validate floor position for object placement
            const objectPosition = new THREE.Vector3(chosenLocation.position.x, 0, chosenLocation.position.z);

            // Check if the DungeonHandler has floor validation available
            if (window.dungeonHandler && typeof window.dungeonHandler._hasValidFloor === 'function') {
                if (!window.dungeonHandler._hasValidFloor(objectPosition, 1.0, objToPlace.type)) {
                    console.warn(`[ObjectPlacement] Object ${objToPlace.type} placed on invalid floor, finding nearest valid position`);

                    // Try to find a nearby valid position
                    if (typeof window.dungeonHandler._findNearestValidFloorPosition === 'function') {
                        const nearestValidPos = window.dungeonHandler._findNearestValidFloorPosition(objectPosition, 3.0);
                        if (nearestValidPos) {
                            objectGroup.position.x = nearestValidPos.x;
                            objectGroup.position.z = nearestValidPos.z;
                            console.log(`[ObjectPlacement] Moved ${objToPlace.type} to valid floor position: (${nearestValidPos.x.toFixed(2)}, ${nearestValidPos.z.toFixed(2)})`);
                        } else {
                            console.warn(`[ObjectPlacement] No valid floor position found for ${objToPlace.type}, using original position`);
                        }
                    }
                }
            }

            objectGroup.position.y = 0;
        }

        roomGroup.add(objectGroup);

        placedObjectData.push({
            position: objectGroup.position.clone(),
            radius: objectRadius
        });
        placedCount++;

        if (objToPlace.isDestructible && objectGroup) {
            // console.log(`[PlaceObjects] Adding destructible object '${objToPlace.type}' to collisionMeshes.`);
            if (objectGroup.isMesh) {
                collisionMeshes.push(objectGroup);
            } else if (objectGroup.isGroup) {
                collisionMeshes.push(objectGroup);
            }
        }

        if (objToPlace.type === 'torch') {
            // Handle torch light creation
            if (objectResult && objectResult.flameLocalPosition) {
                const light = _createTorchLight();
                // Create a temporary vector for the world position calculation
                const worldPos = new THREE.Vector3();
                // Get the world position of the flame
                worldPos.copy(objectResult.flameLocalPosition).applyMatrix4(objectGroup.matrixWorld);
                light.position.copy(worldPos);
                lights.push(light);
            }
        }
        // console.log(`[PlaceObjects][${roomData.id}] Successfully placed '${objToPlace.type}'...`);
    }
    // --- END Place Other Objects ---

    console.log(`[PlaceObjects][${roomData.id}] Finished placing objects. Total successfully placed: ${placedCount} out of ${finalObjectsToPlace.length} initial instances.`);
}

/**
 * Generates the visual elements (walls, floors, doors, interior objects) for a room.
 * Uses the provided area data.
 * Returns an object containing the main group, collision meshes, light objects, bounding box, and door positions.
 */
export function generateRoomVisuals(roomData, areaData) {
    if (!roomData) {
        console.error("[generateRoomVisuals] No room data provided.");
        return { roomGroup: new THREE.Group(), collisionMeshes: [], lights: [], boundingBox: { minX: 0, maxX: 0, minZ: 0, maxZ: 0 }, doorCenterPoints: {} };
    }
    if (!areaData) {
         console.error(`[generateRoomVisuals] Area data is missing for room ${roomData.id}. Cannot generate visuals.`);
        return { roomGroup: new THREE.Group(), collisionMeshes: [], lights: [], boundingBox: { minX: 0, maxX: 0, minZ: 0, maxZ: 0 }, doorCenterPoints: {} };
    }
    console.log(`[generateRoomVisuals] Generating visuals for room ${roomData.id} using area '${areaData.name}'`);

    const roomGroup = new THREE.Group();
    roomGroup.position.set(0, 0, 0); // Room origin
    const collisionMeshes = [];
    const lights = [];
    const doorCenterPoints = { n: null, s: null, e: null, w: null };
    const doorPlaced = { n: false, s: false, e: false, w: false };

    // --- Determine if room is dark ---
    let isDarkRoom = Math.random() < DARK_ROOM_PROBABILITY;
    if (isDarkRoom && areaData.name !== 'The Catacombs') {
        console.log(`[generateRoomVisuals] Room ${roomData.id} is a dark room.`);
    } else if (isDarkRoom && areaData.name === 'The Catacombs') {
         console.log(`[generateRoomVisuals] Room ${roomData.id} is Catacombs, overriding dark room.`);
         isDarkRoom = false;
    }
    // --- Store the result on roomData ---
    roomData.isDark = isDarkRoom;
    // ----------------------------------

    // Calculate room dimensions and bounding box early
    let roomWidth, roomDepth, halfWidth, halfDepth, boundingBox;
    const shapeKey = roomData.shapeKey || 'SQUARE_1X1';
    switch (shapeKey) {
        case 'BOSS_ARENA':
            const B = 2 * ROOM_WORLD_SIZE; // Boss room is 2x larger
            roomWidth = 2 * B;  // 4x standard room width
            roomDepth = 2 * B;  // 4x standard room depth
            break;
        case 'RECTANGULAR': case 'RECT_2X1':
            roomWidth = ROOM_WORLD_SIZE * 2;
            roomDepth = ROOM_WORLD_SIZE;
            break;
        case 'L_SHAPE': roomWidth = ROOM_WORLD_SIZE * 2; roomDepth = ROOM_WORLD_SIZE * 2; break;
        case 'T_SHAPE': roomWidth = ROOM_WORLD_SIZE * 3; roomDepth = ROOM_WORLD_SIZE * 2; break;
        case 'CROSS_SHAPE': roomWidth = ROOM_WORLD_SIZE * 3; roomDepth = ROOM_WORLD_SIZE * 3; break;
        case 'SQUARE_2X2': roomWidth = ROOM_WORLD_SIZE * 2; roomDepth = ROOM_WORLD_SIZE * 2; break;
        case 'RECT_3X1': roomWidth = ROOM_WORLD_SIZE * 3; roomDepth = ROOM_WORLD_SIZE; break;
        case 'RECT_1X2': roomWidth = ROOM_WORLD_SIZE; roomDepth = ROOM_WORLD_SIZE * 2; break;
        case 'RECT_1X3': roomWidth = ROOM_WORLD_SIZE; roomDepth = ROOM_WORLD_SIZE * 3; break;
        case 'RECT_3X2': roomWidth = ROOM_WORLD_SIZE * 3; roomDepth = ROOM_WORLD_SIZE * 2; break;
        case 'U_SHAPE_DOWN': roomWidth = ROOM_WORLD_SIZE * 3; roomDepth = ROOM_WORLD_SIZE * 2; break;
        case 'SQUARE_1X1': default: roomWidth = ROOM_WORLD_SIZE; roomDepth = ROOM_WORLD_SIZE; break;
    }
    halfWidth = roomWidth / 2;
    halfDepth = roomDepth / 2;
    boundingBox = _calculateRoomBoundingBox(shapeKey, roomWidth, roomDepth);

    // Store the calculated bounds in roomData for object placement
    roomData.bounds = {
        width: roomWidth,
        depth: roomDepth,
        minX: boundingBox.minX,
        maxX: boundingBox.maxX,
        minZ: boundingBox.minZ,
        maxZ: boundingBox.maxZ
    };

    // Get room geometry segments with updated dimensions
    const { wallSegments, floorSegments } = _getRoomGeometrySegments(shapeKey, roomWidth, roomDepth, halfWidth, halfDepth, roomData.connections);
    const roomMaxZ = boundingBox.maxZ;

    // Use the environment type manager to select floor, wall, and door types
    const seed = roomData.id * 1000 + 37; // Generate a seed based on room ID
    const environmentTypes = environmentTypeManager.selectEnvironmentTypes({
        biome: areaData.name.toLowerCase().replace(/\s+/g, '_').replace(/^the_/, ''),
        roomType: roomData.type.toLowerCase(),
        seed
    });

    console.log(`[generateRoomVisuals] Selected environment types for room ${roomData.id}:`, environmentTypes);

    const floorFunc = getPrefabFunction(environmentTypes.floorType, 'floor');
    const wallFunc = getPrefabFunction(environmentTypes.wallType, 'wall');
    const doorFunc = getPrefabFunction(environmentTypes.doorType, 'door');

    // 1. Add Floor Segments
    console.log(`[generateRoomVisuals] Adding ${floorSegments.length} floor segment(s) for room ${roomData.id}`);
    floorSegments.forEach(floorSeg => {
        addFloorSegment(roomGroup, collisionMeshes, floorSeg.width, floorSeg.depth, floorSeg.position, roomData, floorFunc);
    });

    // 2. Add Wall Segments and Doors
    console.log(`[generateRoomVisuals] Adding ${wallSegments.length} wall segment(s) for room ${roomData.id}`);
    const addedWallSegmentsData = []; // Store wall segment data for interior object placement
    wallSegments.forEach((wallSeg) => {
        const { position, rotation, length, isOuterBoundary, direction } = wallSeg;
        const connectionDirLower = direction ? direction.toLowerCase() : null;

        const isDoorCandidate = isOuterBoundary &&
                                connectionDirLower &&
                                roomData.connections &&
                                roomData.connections[connectionDirLower] !== null &&
                                roomData.connections[connectionDirLower] !== undefined;

        const isDoorLocation = isDoorCandidate && !doorPlaced[connectionDirLower];

        // Determine visibility BEFORE adding data
        let isVisible = true;
        const epsilon = 0.1;
        if (position.z >= roomMaxZ - epsilon) {
            isVisible = false;
        }

        // ALWAYS add a wall segment regardless of whether a door is present
        addWallSegment(roomGroup, collisionMeshes, lights, length, position, rotation, isOuterBoundary, isDarkRoom, roomData, roomMaxZ, wallFunc);

        // Add wall segment data for object placement
        addedWallSegmentsData.push({
            position, rotation, length, direction, isOuterBoundary, isVisible,
            // Add explicit visibility check for south wall
            isVisible: position.z < roomMaxZ - epsilon
        });

        // If a door should be here, add it AFTER the wall is already placed
        if (isDoorLocation) {
            console.log(`[GenVis][${roomData.id}] Calling addDoorPrefab for direction ${direction.toUpperCase()} at pos ${position.x.toFixed(1)},${position.z.toFixed(1)}`);

            // Calculate door position with a small inward offset from the wall
            // First determine the inward direction vector based on the wall direction
            const inwardVector = new THREE.Vector3();
            switch(connectionDirLower) {
                case 'n': inwardVector.set(0, 0, 1); break;  // North wall - inward is +Z (south)
                case 's': inwardVector.set(0, 0, -1); break; // South wall - inward is -Z (north)
                case 'e': inwardVector.set(-1, 0, 0); break; // East wall - inward is -X (west)
                case 'w': inwardVector.set(1, 0, 0); break;  // West wall - inward is +X (east)
            }

            // Calculate the inward door position by offsetting from the wall position
            const inwardOffset = 0.8; // Significantly increased offset to make doors come out more from the wall
            const doorPosition = position.clone().add(
                inwardVector.clone().multiplyScalar(inwardOffset)
            );

            // Set Y position to 0 to ensure door is at floor level
            doorPosition.y = 0;

            // Create a door at this adjusted inward position
            const doorGroup = addDoorPrefab(roomGroup, collisionMeshes, doorPosition, rotation, roomData, doorFunc);

            if (doorGroup) {
                roomGroup.add(doorGroup);
                doorCenterPoints[connectionDirLower] = doorGroup.position.clone();
                doorPlaced[connectionDirLower] = true;
            } else {
                console.error(`[GenVis][${roomData.id}-${shapeKey}] Failed to create Door Prefab for [${direction.toUpperCase()}].`);
            }
        }
    });

    // --- 4. Place Interior Objects using the environment object manager ---
    if (roomData.id !== 0) { // Skip for start room (handled separately)
        // Use the environment object manager to select and place objects
        const objectSeed = roomData.id * 1000 + 73; // Generate a seed based on room ID
        const biome = areaData.name.toLowerCase().replace(/\s+/g, '_').replace(/^the_/, '');

        // Choose objects to place
        console.log(`[Room ${roomData.id}] Choosing objects for biome: ${biome}, roomType: ${roomData.type.toLowerCase()}`);
        const objectsToPlace = environmentObjectManager.chooseObjectGroup({
            biome,
            roomType: roomData.type.toLowerCase(),
            roomData,
            seed: objectSeed
        });

        console.log(`[Room ${roomData.id}] Objects to place:`, objectsToPlace.map(obj => `${obj.type} (${obj.placement})`));

        // Get placement positions
        const roomDimensions = {
            width: roomWidth,
            depth: roomDepth,
            minX: -halfWidth,
            maxX: halfWidth,
            minZ: -halfDepth,
            maxZ: halfDepth
        };

        const objectPlacements = environmentObjectManager.getObjectPlacements(
            objectsToPlace,
            roomDimensions,
            objectSeed,
            shapeKey  // CRITICAL FIX: Pass room shape for validation
        );

        // Create object instances
        console.log(`[Room ${roomData.id}] Creating object instances for ${objectPlacements.length} placements`);
        const objectInstances = environmentObjectManager.createObjectInstances(objectPlacements);

        // Place objects in the room
        console.log(`[Room ${roomData.id}] Placing ${objectInstances.length} objects in the room`);
        console.log(`[Room ${roomData.id}] Object instances:`, objectInstances.map(inst => `${inst.type} (${inst.options.placement})`));
        objectInstances.forEach((instance, index) => {
            const { prefabFunc, options, type } = instance;
            console.log(`[Room ${roomData.id}] Creating object ${index + 1}/${objectInstances.length}: ${type}`);

            try {
                const objectResult = prefabFunc(options);

                // Handle both return types: object with group property or direct group
                const objectGroup = objectResult && objectResult.group ? objectResult.group : objectResult;

                if (objectGroup && objectGroup instanceof THREE.Object3D) {
                    console.log(`[Room ${roomData.id}] Successfully created object: ${type}`);
                    console.log(`[Room ${roomData.id}] Object userData:`, objectGroup.userData);
                    console.log(`[Room ${roomData.id}] isDestructible: ${objectGroup.userData?.isDestructible}`);

                    // Set position and rotation based on options
                    console.log(`[Room ${roomData.id}] Object options:`, options);

                    if (options.position) {
                        console.log(`[Room ${roomData.id}] Setting position: x=${options.position.x}, y=${options.position.y}, z=${options.position.z}`);
                        objectGroup.position.set(options.position.x, options.position.y, options.position.z);
                    }

                    if (options.rotation !== undefined) {
                        console.log(`[Room ${roomData.id}] Setting rotation: ${options.rotation}`);
                        objectGroup.rotation.y = options.rotation;
                    }

                    // Adjust height and orientation based on placement type
                    if (options.placement === 'wall') {
                        console.log(`[Room ${roomData.id}] Processing wall object: ${type}`);

                        // ENHANCED Wall placement logic with proper offsets
                        objectGroup.position.copy(options.position);
                        objectGroup.lookAt(objectGroup.position.clone().add(options.inwardNormal));

                        // CRITICAL FIX: Object-specific inward offsets for proper wall alignment
                        let inwardOffset = 0.4; // INCREASED: Much larger base offset to close gaps
                        if (type === 'vine') {
                            inwardOffset = 0.3; // INCREASED: Vines closer to wall but still substantial offset
                        } else if (type === 'torch') {
                            inwardOffset = 0.5; // INCREASED: Torches need significant offset to touch wall
                        } else if (type === 'aether_torch') {
                            inwardOffset = 0.5; // INCREASED: Aether torches same as regular torches
                        }

                        objectGroup.position.addScaledVector(options.inwardNormal, inwardOffset);
                        let yPos = WALL_HEIGHT * 0.5;
                        if (type === 'vine') yPos = WALL_HEIGHT * 0.85;
                        else if (type === 'torch') yPos = WALL_HEIGHT * 0.6;
                        objectGroup.position.y = yPos;
                    } else { // Floor placement
                        // CRITICAL FIX: Validate floor position for object placement
                        const objectPosition = new THREE.Vector3(options.position.x, 0, options.position.z);

                        // Check if the DungeonHandler has floor validation available
                        if (window.dungeonHandler && typeof window.dungeonHandler._hasValidFloor === 'function') {
                            if (!window.dungeonHandler._hasValidFloor(objectPosition, 1.0, type)) {
                                console.warn(`[ObjectPlacement] Object ${type} placed on invalid floor, finding nearest valid position`);

                                // Try to find a nearby valid position
                                if (typeof window.dungeonHandler._findNearestValidFloorPosition === 'function') {
                                    const nearestValidPos = window.dungeonHandler._findNearestValidFloorPosition(objectPosition, 3.0);
                                    if (nearestValidPos) {
                                        objectGroup.position.x = nearestValidPos.x;
                                        objectGroup.position.z = nearestValidPos.z;
                                        console.log(`[ObjectPlacement] Moved ${type} to valid floor position: (${nearestValidPos.x.toFixed(2)}, ${nearestValidPos.z.toFixed(2)})`);
                                    } else {
                                        console.warn(`[ObjectPlacement] No valid floor position found for ${type}, using original position`);
                                    }
                                }
                            }
                        }

                        objectGroup.position.y = 0;
                    }

                    roomGroup.add(objectGroup);

                    // Add collision meshes
                    objectGroup.traverse(child => {
                        if (child.isMesh) {
                            collisionMeshes.push(child);
                            console.log(`[Room ${roomData.id}] Added collision mesh for ${type}:`, child);
                        }
                    });

                    // Add lights if the object has them
                    if (objectGroup.userData && objectGroup.userData.lights) {
                        objectGroup.userData.lights.forEach(light => {
                            lights.push(light);
                        });
                    }

                    // Handle torch light creation
                    if (type === 'torch' && objectResult && objectResult.flameLocalPosition) {
                        const light = _createTorchLight();
                        light.position.copy(objectGroup.localToWorld(objectResult.flameLocalPosition.clone()));
                        lights.push(light);
                    }
                } else {
                    console.warn(`[Room ${roomData.id}] Prefab function returned null for object: ${type}`);
                }
            } catch (error) {
                console.error(`[Room ${roomData.id}] Error creating object ${type}:`, error);
            }
        });

        console.log(`[generateRoomVisuals] Placed ${objectInstances.length} objects in room ${roomData.id}`);
    }

    // --- NEW: Special Handling for Room 0 ---
    if (roomData.id === 0) {
        console.log(`[generateRoomVisuals][${roomData.id}] Placing Ritual Circle and Aether Torches.`);

        // Place Ritual Circle (already existing code)
        const ritualCirclePrefabFunc = getPrefabFunction('ritual_circle', 'interior');
        if (ritualCirclePrefabFunc) {
            const ritualCircleGroup = ritualCirclePrefabFunc();
            if (ritualCircleGroup) {
                ritualCircleGroup.position.set(0, 0, 0);
                roomGroup.add(ritualCircleGroup);
                console.log(`[generateRoomVisuals][${roomData.id}] Ritual Circle added at room center.`);
            } else {
                console.warn(`[generateRoomVisuals][${roomData.id}] Failed to create ritual_circle prefab.`);
            }
        } else {
            console.warn(`[generateRoomVisuals][${roomData.id}] Could not find ritual_circle prefab function.`);
        }

        // Place Aether Torches around the circle
        const aetherTorchPrefabFunc = getPrefabFunction('aether_torch', 'interior');
        if (aetherTorchPrefabFunc) {
            const numTorches = 6; // Place 6 torches
            const torchRadius = VOXEL_SIZE * 25; // Radius from center (adjust as needed)
            const angleStep = (Math.PI * 2) / numTorches;

            for (let i = 0; i < numTorches; i++) {
                const angle = i * angleStep;
                const torchX = Math.cos(angle) * torchRadius;
                const torchZ = Math.sin(angle) * torchRadius;
                const torchYOffset = 0.01; // Small offset to avoid floor clipping

                const torchResult = aetherTorchPrefabFunc();
                if (torchResult && torchResult.group) {
                    const torchGroup = torchResult.group;
                    torchGroup.position.set(torchX, torchYOffset, torchZ);
                    // Optional: Rotate torch to face center? Or keep upright?
                    // torchGroup.lookAt(new THREE.Vector3(0, torchYOffset, 0)); // Faces center
                    roomGroup.add(torchGroup);

                    // Add the blue point light
                    if (torchResult.lightLocalPosition && torchResult.lightColor && torchResult.lightIntensity && torchResult.lightDistance) {
                        const light = new THREE.PointLight(
                            torchResult.lightColor,
                            torchResult.lightIntensity,
                            torchResult.lightDistance
                        );
                        // Convert light's local position to world position
                        const lightWorldPos = torchGroup.localToWorld(torchResult.lightLocalPosition.clone());
                        light.position.copy(lightWorldPos);
                        light.castShadow = false; // Torches usually don't cast strong shadows
                        light.name = `aetherTorchLight_${i}`;
                        lights.push(light); // Add to the room's light array
                        console.log(`[generateRoomVisuals][${roomData.id}] Added light for Aether Torch ${i}.`);
                    }
                } else {
                    console.warn(`[generateRoomVisuals][${roomData.id}] Failed to create aether_torch prefab instance ${i}.`);
                }
            }
            console.log(`[generateRoomVisuals][${roomData.id}] Added ${numTorches} Aether Torches around circle.`);
        } else {
            console.warn(`[generateRoomVisuals][${roomData.id}] Could not find aether_torch prefab function.`);
        }
    }
    // --- END Special Handling for Room 0 ---

    // --- 5. Add Room Effects (e.g., Mist) ---
    if (areaData.name === 'The Catacombs') {
        const mistPlanes = createFloorMistPlanes(boundingBox, 1.0, 3, 0.03, floorSegments); // Pass floor segments instead of wall buffer
        roomGroup.add(mistPlanes);
        console.log(`[GenVis][${roomData.id}] Added floor mist planes for Catacombs.`);
    }
    // --- END Floor Fog ---

    console.log(`[generateRoomVisuals] Completed visuals for room ${roomData.id}. Meshes: ${collisionMeshes.length}, Lights: ${lights.length}`);

    // CRITICAL FIX: Store wall segment data for torch placement validation
    if (window.dungeonHandler && typeof window.dungeonHandler._setCurrentRoomWallSegments === 'function') {
        window.dungeonHandler._setCurrentRoomWallSegments(addedWallSegmentsData);
    }

    // CRITICAL FIX: Store floor mesh data for floor object placement validation
    if (window.dungeonHandler && typeof window.dungeonHandler._setCurrentRoomFloorMeshes === 'function') {
        // Filter collision meshes to get only floor meshes
        const floorMeshes = collisionMeshes.filter(mesh => mesh.userData && mesh.userData.isFloor);
        window.dungeonHandler._setCurrentRoomFloorMeshes(floorMeshes);
    }

    return { roomGroup, collisionMeshes, lights, boundingBox, doorCenterPoints };
}

// Helper function to calculate offset positions for wall segments beside doors
function calculateOffsetPosition(basePosition, rotation, offsetX, offsetZ) {
    // Create a rotation quaternion
    const rotationQuat = new THREE.Quaternion().setFromEuler(
        new THREE.Euler(0, rotation, 0)
    );

    // Create offset vector and apply rotation
    const offsetVec = new THREE.Vector3(offsetX, 0, offsetZ);
    offsetVec.applyQuaternion(rotationQuat);

    // Return new position
    return new THREE.Vector3(
        basePosition.x + offsetVec.x,
        basePosition.y,
        basePosition.z + offsetVec.z
    );
}

// --- NEW: Helper function to calculate room bounding box ---
// This simplifies getting bounds for different shapes
function _calculateRoomBoundingBox(shapeKey, roomWidth, roomDepth) {
    const halfWidth = roomWidth / 2;
    const halfDepth = roomDepth / 2;
    const R = ROOM_WORLD_SIZE;
    const H = R / 2;
    const B = 2 * R; // Boss room size multiplier

    let minX, maxX, minZ, maxZ;

    switch (shapeKey) {
        case 'BOSS_ARENA':
            minX = -B;  // Use full boss room size
            maxX = B;
            minZ = -B;
            maxZ = B;
            break;
        case 'RECTANGULAR': case 'RECT_2X1': case 'RECT_1X2':
        case 'SQUARE_1X1': case 'SQUARE_2X2':
        case 'RECT_3X1': case 'RECT_1X3': case 'RECT_3X2':
            minX = -halfWidth; maxX = halfWidth;
            minZ = -halfDepth; maxZ = halfDepth;
            break;
        case 'L_SHAPE': // L-shape with vertical part on left and horizontal part on bottom
            minX = -R; maxX = R;  // -14 to 14
            minZ = -R; maxZ = R;  // -14 to 14
            break;
        case 'T_SHAPE': // Assumes origin at junction
            minX = -(R + H); maxX = R + H; // -21 to 21
            minZ = -R; maxZ = R;      // -14 to 14
            break;
        case 'CROSS_SHAPE': // Assumes origin at center
            minX = -(R+H); maxX = R+H; // -21 to 21
            minZ = -(R+H); maxZ = R+H; // -21 to 21
            break;
        case 'U_SHAPE_DOWN': // Assumes origin at center of top bar
            minX = -(R+H); maxX = R+H; // -21 to 21
            minZ = -R; maxZ = R;      // -14 to 14
            break;
        default:
            console.warn(`[CalcBounds] Unknown shapeKey: ${shapeKey}. Defaulting to SQUARE_1X1.`);
            minX = -H; maxX = H; minZ = -H; maxZ = H;
            break;
    }

    return { minX, maxX, minZ, maxZ, width: maxX - minX, depth: maxZ - minZ };
}
// --- END Helper ---

console.log("roomGenerator.js loaded - Refactored for areaData and interior objects");