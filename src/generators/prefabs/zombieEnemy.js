/**
 * Creates a voxel-based zombie enemy model.
 * Uses standard VOXEL_SIZE with sub-voxels for the head.
 * Includes named groups for animation.
 */

import * as THREE from 'three';
import * as BufferGeometryUtils from 'three/addons/utils/BufferGeometryUtils.js';
import {
    VOXEL_SIZE,
    mulberry32,
    getOrCreateGeometry
} from './shared.js';

/**
 * Creates a voxel-based zombie enemy model.
 * @param {number} [scale=1.0] Optional overall scale factor.
 * @returns {THREE.Group} The zombie enemy model group.
 */
export function createZombieEnemyModel(scale = 1.0) {
    const finalGroup = new THREE.Group();
    finalGroup.name = "Zombie";

    // Create a seeded random function
    const random = mulberry32(12345); // Fixed seed for consistent appearance

    // Create template geometries
    const voxelGeo = new THREE.BoxGeometry(VOXEL_SIZE, VOXEL_SIZE, VOXEL_SIZE);

    // --- Dimensions ---
    // Larger zombie with 15 units total height
    const legHeight = 7; // Height of legs (7 units)
    const torsoHeight = 6; // Height of torso (6 units)
    const headHeight = 2; // Height of head (2 units)
    // Total height: legHeight + torsoHeight + headHeight = 15 units

    // --- Create Body Parts ---

    // Create body group
    const bodyGroup = new THREE.Group();
    bodyGroup.name = 'body';
    finalGroup.add(bodyGroup);
    console.log('Created body group:', bodyGroup.name);

    // Create torso
    const torsoWidth = 3;
    const torsoDepth = 2;
    for (let y = 0; y < torsoHeight; y++) {
        for (let x = -Math.floor(torsoWidth/2); x < Math.ceil(torsoWidth/2); x++) {
            for (let z = -Math.floor(torsoDepth/2); z < Math.ceil(torsoDepth/2); z++) {
                // Skip some voxels for more organic shape
                if ((Math.abs(x) == Math.floor(torsoWidth/2) && Math.abs(z) == Math.floor(torsoDepth/2)) && random() < 0.3) continue;

                // Create torso voxel
                const voxel = new THREE.Mesh(
                    voxelGeo.clone(),
                    new THREE.MeshLambertMaterial({
                        color: getRandomZombieColor(random)
                    })
                );

                voxel.position.set(
                    x * VOXEL_SIZE,
                    y * VOXEL_SIZE + legHeight * VOXEL_SIZE, // Position above legs
                    z * VOXEL_SIZE
                );

                bodyGroup.add(voxel);
            }
        }
    }

    // Create head group
    const headGroup = new THREE.Group();
    headGroup.name = 'head';
    finalGroup.add(headGroup);
    console.log('Created head group:', headGroup.name);

    // Position head on top of torso
    headGroup.position.y = (legHeight + torsoHeight) * VOXEL_SIZE;

    // Create head
    const headSize = 2;
    for (let y = 0; y < headHeight; y++) {
        for (let x = -Math.floor(headSize/2); x <= Math.floor(headSize/2); x++) {
            for (let z = -Math.floor(headSize/2); z <= Math.floor(headSize/2); z++) {
                // Skip corners for rounded shape
                if (Math.abs(x) == Math.floor(headSize/2) &&
                    Math.abs(z) == Math.floor(headSize/2) &&
                    y == headHeight-1) continue;

                // Create head voxel
                const voxel = new THREE.Mesh(
                    voxelGeo.clone(),
                    new THREE.MeshLambertMaterial({
                        color: getRandomZombieColor(random)
                    })
                );

                voxel.position.set(
                    x * VOXEL_SIZE,
                    y * VOXEL_SIZE,
                    z * VOXEL_SIZE
                );

                headGroup.add(voxel);
            }
        }
    }

    // Add eyes (red)
    const leftEye = new THREE.Mesh(
        voxelGeo.clone(),
        new THREE.MeshLambertMaterial({
            color: 0xff0000,
            emissive: 0xff0000,
            emissiveIntensity: 0.5
        })
    );
    leftEye.position.set(-0.5 * VOXEL_SIZE, 0.5 * VOXEL_SIZE, 1.1 * VOXEL_SIZE);
    leftEye.scale.set(0.5, 0.5, 0.5);
    headGroup.add(leftEye);

    const rightEye = new THREE.Mesh(
        voxelGeo.clone(),
        new THREE.MeshLambertMaterial({
            color: 0xff0000,
            emissive: 0xff0000,
            emissiveIntensity: 0.5
        })
    );
    rightEye.position.set(0.5 * VOXEL_SIZE, 0.5 * VOXEL_SIZE, 1.1 * VOXEL_SIZE);
    rightEye.scale.set(0.5, 0.5, 0.5);
    headGroup.add(rightEye);

    // Add mouth (black)
    for (let x = -1; x <= 1; x++) {
        const mouthPart = new THREE.Mesh(
            voxelGeo.clone(),
            new THREE.MeshLambertMaterial({ color: 0x000000 })
        );
        mouthPart.position.set(x * 0.5 * VOXEL_SIZE, -0.5 * VOXEL_SIZE, 1.1 * VOXEL_SIZE);
        mouthPart.scale.set(0.5, 0.5, 0.5);
        headGroup.add(mouthPart);
    }

    // Create left arm group
    const leftArmGroup = new THREE.Group();
    leftArmGroup.name = 'leftArm';
    finalGroup.add(leftArmGroup);
    console.log('Created left arm group:', leftArmGroup.name);

    // Position left arm
    leftArmGroup.position.set(
        -2 * VOXEL_SIZE, // Offset from center
        (legHeight + torsoHeight - 1) * VOXEL_SIZE, // Attach at shoulder height
        0
    );

    // Create left arm
    const armLength = 6;
    for (let y = 0; y > -armLength; y--) {
        const armVoxel = new THREE.Mesh(
            voxelGeo.clone(),
            new THREE.MeshLambertMaterial({
                color: getRandomZombieColor(random)
            })
        );

        armVoxel.position.set(0, y * VOXEL_SIZE, 0);
        leftArmGroup.add(armVoxel);
    }

    // Create right arm group
    const rightArmGroup = new THREE.Group();
    rightArmGroup.name = 'rightArm';
    finalGroup.add(rightArmGroup);
    console.log('Created right arm group:', rightArmGroup.name);

    // Position right arm
    rightArmGroup.position.set(
        2 * VOXEL_SIZE, // Offset from center
        (legHeight + torsoHeight - 1) * VOXEL_SIZE, // Attach at shoulder height
        0
    );

    // Create right arm
    for (let y = 0; y > -armLength; y--) {
        const armVoxel = new THREE.Mesh(
            voxelGeo.clone(),
            new THREE.MeshLambertMaterial({
                color: getRandomZombieColor(random)
            })
        );

        armVoxel.position.set(0, y * VOXEL_SIZE, 0);
        rightArmGroup.add(armVoxel);
    }

    // Create left leg group
    const leftLegGroup = new THREE.Group();
    leftLegGroup.name = 'leftLeg';
    finalGroup.add(leftLegGroup);
    console.log('Created left leg group:', leftLegGroup.name);

    // Position left leg
    leftLegGroup.position.set(
        -1 * VOXEL_SIZE, // Offset from center
        legHeight * VOXEL_SIZE, // Position at bottom of torso
        0
    );

    // Create left leg
    for (let y = 0; y > -legHeight; y--) {
        const legVoxel = new THREE.Mesh(
            voxelGeo.clone(),
            new THREE.MeshLambertMaterial({
                color: getRandomZombieColor(random)
            })
        );

        legVoxel.position.set(0, y * VOXEL_SIZE, 0);
        leftLegGroup.add(legVoxel);
    }

    // Create right leg group
    const rightLegGroup = new THREE.Group();
    rightLegGroup.name = 'rightLeg';
    finalGroup.add(rightLegGroup);
    console.log('Created right leg group:', rightLegGroup.name);

    // Position right leg
    rightLegGroup.position.set(
        1 * VOXEL_SIZE, // Offset from center
        legHeight * VOXEL_SIZE, // Position at bottom of torso
        0
    );

    // Create right leg
    for (let y = 0; y > -legHeight; y--) {
        const legVoxel = new THREE.Mesh(
            voxelGeo.clone(),
            new THREE.MeshLambertMaterial({
                color: getRandomZombieColor(random)
            })
        );

        legVoxel.position.set(0, y * VOXEL_SIZE, 0);
        rightLegGroup.add(legVoxel);
    }

    // Apply overall scale
    finalGroup.scale.set(scale, scale, scale);

    // Rotate group to face forward (Z+) by default
    finalGroup.rotation.y = Math.PI;

    // Add animation data to userData - simplified for SimpleZombieAnimationHandler
    finalGroup.userData.animationData = {
        walkSpeed: 1.5,              // Walk speed multiplier
        walkAmplitude: Math.PI / 6,  // Amplitude for leg movement
        armSwingAmplitude: Math.PI / 4, // Amplitude for arm swing
        attackAnimationDuration: 0.6, // REDUCED: Much faster attack animation (was 1.0s)
        idleSpeed: 1.2               // Speed of idle animation
    };

    // Add type information to ensure proper animation handling
    finalGroup.userData.type = 'zombie';

    // Add attack hitbox data
    finalGroup.userData.attackHitbox = {
        radius: 3.5 * scale, // Large attack radius
        damage: 1, // 1 damage as per user preference
        knockback: 7.0 // Strong knockback
    };

    // Log the final structure
    console.log('\nFinal zombie model structure:');
    console.log('- Model type:', finalGroup.userData.type);
    console.log('- Body parts:');
    console.log('  - body:', finalGroup.getObjectByName('body') !== null);
    console.log('  - head:', finalGroup.getObjectByName('head') !== null);
    console.log('  - leftArm:', finalGroup.getObjectByName('leftArm') !== null);
    console.log('  - rightArm:', finalGroup.getObjectByName('rightArm') !== null);
    console.log('  - leftLeg:', finalGroup.getObjectByName('leftLeg') !== null);
    console.log('  - rightLeg:', finalGroup.getObjectByName('rightLeg') !== null);
    console.log('- Animation data:', finalGroup.userData.animationData);

    // Verify all body parts are properly positioned and named
    const bodyParts = ['body', 'head', 'leftArm', 'rightArm', 'leftLeg', 'rightLeg'];
    for (const partName of bodyParts) {
        const part = finalGroup.getObjectByName(partName);
        if (!part) {
            console.error(`Missing required body part: ${partName}`);
        } else {
            console.log(`${partName} position:`, part.position);
            console.log(`${partName} rotation:`, part.rotation);
        }
    }

    console.log("Created New Zombie Enemy Model");
    return finalGroup;
}

/**
 * Get a random zombie color
 * @param {Function} random - Random function
 * @returns {number} - Color as hex
 */
function getRandomZombieColor(random) {
    const zombieColors = [
        0x7da269, // Light green
        0x5a7e4c, // Medium green
        0x3e5a35, // Dark green
        0x2d4026, // Very dark green
        0x9cbc8a, // Pale green
    ];

    return zombieColors[Math.floor(random() * zombieColors.length)];
}
